{"job_description": "\nSoftware Engineer - Python Developer\n\nWe are looking for a skilled Python Developer to join our team.\n\nRequired Skills:\n- Python\n- Django\n- Flask\n- PostgreSQL\n- MySQL\n- REST API\n- Git\n\nPreferred Skills:\n- AWS\n- Azure\n- Docker\n- Machine Learning\n- Agile\n\nRequirements:\n- Bachelor's degree in Computer Science or related field\n- 3+ years of experience in Python development\n- Experience with web frameworks\n- Knowledge of databases\n- Problem-solving skills\n\nPreferred:\n- Master's degree\n- 5+ years of experience\n- Experience with cloud platforms\n- Docker and containerization experience\n- Machine Learning experience\n- Agile development experience\n", "processing_stats": {"total_files": 23, "successful_analyses": 22, "failed_analyses": 0, "processing_time": 266.1378457546234, "average_score": 55.17272727272727, "top_score": 68.2}, "results": [{"rank": 1, "candidate_name": "Vidhyasagar P", "file_name": "Profile_9.pdf", "overall_score": 68.2, "skills_match": 19.166666666666664, "experience_match": 100, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Django, Git", "missing_skills": "Python, REST API, PostgreSQL, MySQL, AWS, Azure, Docker, Machine Learning", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, REST API, PostgreSQL, MySQL, AWS"}, {"rank": 2, "candidate_name": "<PERSON><PERSON>", "file_name": "Profile_12.pdf", "overall_score": 68.0, "skills_match": 15.0, "experience_match": 100, "education_match": 95, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Python, Django, PostgreSQL", "missing_skills": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Machine Learning", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: AWS, Azure, Docker, Machine Learning"}, {"rank": 3, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_8.pdf", "overall_score": 64.7, "skills_match": 7.5, "experience_match": 100, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Java, Backend", "missing_skills": "Python, Django, PostgreSQL, MySQL, REST API, Git, AWS, Azure, Docker, Machine Learning", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, PostgreSQL, MySQL, REST API"}, {"rank": 4, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_1.pdf", "overall_score": 62.5, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "LinkedIn Strategy, Brand Strategy", "missing_skills": "Python, Django, PostgreSQL, MySQL, REST API, Git, AWS, Azure, Docker, Machine Learning", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, PostgreSQL, MySQL, REST API"}, {"rank": 5, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_10.pdf", "overall_score": 62.5, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Python, Django", "missing_skills": "PostgreSQL, MySQL, REST API, Git, AWS, Azure, Docker, Machine Learning", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: PostgreSQL, MySQL, REST API, Git, AWS"}, {"rank": 6, "candidate_name": "<PERSON>", "file_name": "Profile_14.pdf", "overall_score": 62.5, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Python, Django, PostgreSQL", "missing_skills": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Machine Learning", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: AWS, Azure, Docker, Machine Learning"}, {"rank": 7, "candidate_name": "<PERSON><PERSON><PERSON><PERSON> (Raghu) Uppalli", "file_name": "Profile_13.pdf", "overall_score": 62.5, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Python, Django, PostgreSQL, Git", "missing_skills": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Machine Learning", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: AWS, Azure, Docker, Machine Learning"}, {"rank": 8, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_2.pdf", "overall_score": 62.5, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Python, Django, PostgreSQL", "missing_skills": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Machine Learning", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: AWS, Azure, Docker, Machine Learning"}, {"rank": 9, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_18.pdf", "overall_score": 62.5, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Python, Django, PostgreSQL", "missing_skills": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Machine Learning", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: AWS, Azure, Docker, Machine Learning"}, {"rank": 10, "candidate_name": "<PERSON><PERSON>", "file_name": "Profile_5.pdf", "overall_score": 62.5, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Python, Django, PostgreSQL", "missing_skills": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Machine Learning", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: AWS, Azure, Docker, Machine Learning"}, {"rank": 11, "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "file_name": "Profile_7.pdf", "overall_score": 62.5, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Python, Django, PostgreSQL", "missing_skills": "MySQL, REST API, Git, AWS, Azure, Docker, Machine Learning", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: MySQL, REST API, Git, AWS, Azure"}, {"rank": 12, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_17.pdf", "overall_score": 59.2, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 50, "matching_skills": "", "missing_skills": "Python, Django, PostgreSQL, MySQL, REST API, Git", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, PostgreSQL, MySQL, REST API"}, {"rank": 13, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_19.pdf", "overall_score": 59.2, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 50, "matching_skills": "", "missing_skills": "", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Focus on highlighting relevant technical skills more prominently"}, {"rank": 14, "candidate_name": "<PERSON><PERSON>", "file_name": "Profile_4.pdf", "overall_score": 59.2, "skills_match": 0.0, "experience_match": 100, "education_match": 95, "keywords_match": 75.0, "overall_relevance": 40, "matching_skills": "", "missing_skills": "Python, Django, PostgreSQL, MySQL, REST API, Git", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, PostgreSQL, MySQL, REST API"}, {"rank": 15, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_6.pdf", "overall_score": 59.2, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 50, "matching_skills": "", "missing_skills": "", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Focus on highlighting relevant technical skills more prominently"}, {"rank": 16, "candidate_name": "AKURATHI SASIDHAR", "file_name": "sasidhar.pdf", "overall_score": 55.4, "skills_match": 23.33333333333333, "experience_match": 56.666666666666664, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 50, "matching_skills": "", "missing_skills": "", "experience_relevance": "Estimated experience level matches 57% of requirements", "recommendations": "Focus on highlighting relevant technical skills more prominently; Gain more experience to meet the 3+ years requirement"}, {"rank": 17, "candidate_name": "Ka<PERSON>ya E.", "file_name": "Profile_21.pdf", "overall_score": 55.2, "skills_match": 0.0, "experience_match": 100, "education_match": 95, "keywords_match": 75.0, "overall_relevance": 0, "matching_skills": "", "missing_skills": "Python, Django, PostgreSQL, MySQL, REST API, Git", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, PostgreSQL, MySQL, REST API"}, {"rank": 18, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_15.pdf", "overall_score": 44.5, "skills_match": 0.0, "experience_match": 100, "education_match": 0.0, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Python, Django, PostgreSQL", "missing_skills": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Machine Learning", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Machine Learning; Consider pursuing Bachelor's degree if not already obtained"}, {"rank": 19, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_3.pdf", "overall_score": 44.5, "skills_match": 0.0, "experience_match": 100, "education_match": 0.0, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "vector tracing, image resize and crop", "missing_skills": "Python, Django, PostgreSQL, MySQL, REST API, Git, AWS, Azure, Docker, Machine Learning", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, PostgreSQL, MySQL, REST API; Consider pursuing Bachelor's degree if not already obtained"}, {"rank": 20, "candidate_name": "Unknown", "file_name": "Profile_11.pdf", "overall_score": 25.5, "skills_match": 0.0, "experience_match": 0.0, "education_match": 30.0, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Python, Django, PostgreSQL", "missing_skills": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Machine Learning", "experience_relevance": "Estimated experience level matches 0% of requirements", "recommendations": "Consider developing skills in: <PERSON><PERSON>, <PERSON>zure, <PERSON>er, Machine Learning; Gain more experience to meet the 3+ years requirement; Consider pursuing Bachelor's degree if not already obtained"}, {"rank": 21, "candidate_name": "Unknown", "file_name": "Profile_16.pdf", "overall_score": 25.5, "skills_match": 0.0, "experience_match": 0.0, "education_match": 30.0, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Python, Django, PostgreSQL", "missing_skills": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Machine Learning", "experience_relevance": "Estimated experience level matches 0% of requirements", "recommendations": "Consider developing skills in: <PERSON><PERSON>, <PERSON>zure, <PERSON>er, Machine Learning; Gain more experience to meet the 3+ years requirement; Consider pursuing Bachelor's degree if not already obtained"}, {"rank": 22, "candidate_name": "Unknown", "file_name": "Profile_20.pdf", "overall_score": 25.5, "skills_match": 0.0, "experience_match": 0.0, "education_match": 30.0, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Python, Django, PostgreSQL", "missing_skills": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Machine Learning", "experience_relevance": "Estimated experience level matches 0% of requirements", "recommendations": "Consider developing skills in: <PERSON><PERSON>, <PERSON>zure, <PERSON>er, Machine Learning; Gain more experience to meet the 3+ years requirement; Consider pursuing Bachelor's degree if not already obtained"}]}