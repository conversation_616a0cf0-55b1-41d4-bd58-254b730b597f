"""
Validation Module
Validates AI responses to prevent hallucination and ensure accuracy
"""

import logging
import re
from typing import Dict, Any, List, Optional

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ResponseValidator:
    """Validates AI responses against original resume text"""
    
    def __init__(self):
        self.debug_enabled = True
    
    def validate_resume_analysis(self, analysis_result: Dict[str, Any], original_text: str) -> Dict[str, Any]:
        """Validate resume analysis results against original text"""
        if not analysis_result or not original_text:
            return analysis_result
        
        validated_result = analysis_result.copy()
        original_text_lower = original_text.lower()
        
        # Validate skills
        validated_result["skills"] = self._validate_skills(
            analysis_result.get("skills", []), 
            original_text_lower
        )
        
        # Validate contact info
        validated_result["contact_info"] = self._validate_contact_info(
            analysis_result.get("contact_info", {}), 
            original_text
        )
        
        # Validate experience
        validated_result["experience"] = self._validate_experience(
            analysis_result.get("experience", []), 
            original_text_lower
        )
        
        # Validate education
        validated_result["education"] = self._validate_education(
            analysis_result.get("education", []), 
            original_text_lower
        )
        
        # Validate certifications
        validated_result["certifications"] = self._validate_certifications(
            analysis_result.get("certifications", []), 
            original_text_lower
        )
        
        if self.debug_enabled:
            self._log_validation_results(analysis_result, validated_result)
        
        return validated_result
    
    def _validate_skills(self, extracted_skills: List[str], original_text: str) -> List[str]:
        """Validate that extracted skills actually appear in the resume text"""
        validated_skills = []
        
        for skill in extracted_skills:
            if self._skill_exists_in_text(skill, original_text):
                validated_skills.append(skill)
            else:
                if self.debug_enabled:
                    print(f"🚨 VALIDATION: Removed hallucinated skill: '{skill}' (not found in resume)")
        
        return validated_skills
    
    def _skill_exists_in_text(self, skill: str, text: str) -> bool:
        """Check if a skill actually exists in the resume text"""
        skill_lower = skill.lower().strip()
        
        # Direct match
        if skill_lower in text:
            return True
        
        # Check for variations (e.g., "python" vs "python programming")
        skill_words = skill_lower.split()
        if len(skill_words) == 1:
            # For single word skills, check with word boundaries
            pattern = r'\b' + re.escape(skill_lower) + r'\b'
            if re.search(pattern, text):
                return True
        
        # Check for partial matches for compound skills
        if len(skill_words) > 1:
            # All words of the skill should be present
            return all(word in text for word in skill_words)
        
        return False
    
    def _validate_contact_info(self, contact_info: Dict[str, str], original_text: str) -> Dict[str, str]:
        """Validate contact information against original text"""
        validated_contact = {}
        
        for key, value in contact_info.items():
            if value and value.strip():
                if self._contact_exists_in_text(value, original_text):
                    validated_contact[key] = value
                else:
                    if self.debug_enabled:
                        print(f"🚨 VALIDATION: Removed hallucinated contact {key}: '{value}'")
                    validated_contact[key] = ""
            else:
                validated_contact[key] = ""
        
        return validated_contact
    
    def _contact_exists_in_text(self, contact_value: str, text: str) -> bool:
        """Check if contact information exists in text"""
        if not contact_value or not contact_value.strip():
            return False
        
        # For names, check if substantial part exists
        if len(contact_value.split()) > 1:  # Likely a name
            words = contact_value.lower().split()
            # At least half the words should be present
            matches = sum(1 for word in words if word in text.lower())
            return matches >= len(words) / 2
        
        # For other contact info, check direct presence
        return contact_value.lower() in text.lower()
    
    def _validate_experience(self, experience_list: List[Dict], original_text: str) -> List[Dict]:
        """Validate experience entries"""
        validated_experience = []
        
        for exp in experience_list:
            if self._experience_exists_in_text(exp, original_text):
                validated_experience.append(exp)
            else:
                if self.debug_enabled:
                    print(f"🚨 VALIDATION: Removed hallucinated experience: {exp.get('title', 'Unknown')} at {exp.get('company', 'Unknown')}")
        
        return validated_experience
    
    def _experience_exists_in_text(self, experience: Dict, text: str) -> bool:
        """Check if experience entry exists in text"""
        text_lower = text.lower()
        
        # Check if key components exist
        title = experience.get("title", "").lower()
        company = experience.get("company", "").lower()
        
        if not title and not company:
            return False
        
        # At least one of title or company should be present
        title_exists = title and any(word in text_lower for word in title.split() if len(word) > 2)
        company_exists = company and any(word in text_lower for word in company.split() if len(word) > 2)
        
        return title_exists or company_exists
    
    def _validate_education(self, education_list: List[Dict], original_text: str) -> List[Dict]:
        """Validate education entries"""
        validated_education = []
        
        for edu in education_list:
            if self._education_exists_in_text(edu, original_text):
                validated_education.append(edu)
            else:
                if self.debug_enabled:
                    print(f"🚨 VALIDATION: Removed hallucinated education: {edu.get('degree', 'Unknown')} from {edu.get('institution', 'Unknown')}")
        
        return validated_education
    
    def _education_exists_in_text(self, education: Dict, text: str) -> bool:
        """Check if education entry exists in text"""
        text_lower = text.lower()
        
        degree = education.get("degree", "").lower()
        institution = education.get("institution", "").lower()
        
        if not degree and not institution:
            return False
        
        # Check for degree keywords
        degree_exists = degree and any(word in text_lower for word in degree.split() if len(word) > 2)
        institution_exists = institution and any(word in text_lower for word in institution.split() if len(word) > 2)
        
        return degree_exists or institution_exists
    
    def _validate_certifications(self, certifications: List[str], original_text: str) -> List[str]:
        """Validate certifications"""
        validated_certs = []
        
        for cert in certifications:
            if self._certification_exists_in_text(cert, original_text):
                validated_certs.append(cert)
            else:
                if self.debug_enabled:
                    print(f"🚨 VALIDATION: Removed hallucinated certification: '{cert}'")
        
        return validated_certs
    
    def _certification_exists_in_text(self, certification: str, text: str) -> bool:
        """Check if certification exists in text"""
        cert_lower = certification.lower()
        text_lower = text.lower()
        
        # Check for direct match or key words
        if cert_lower in text_lower:
            return True
        
        # Check for key words in certification
        cert_words = [word for word in cert_lower.split() if len(word) > 2]
        if cert_words:
            return any(word in text_lower for word in cert_words)
        
        return False
    
    def validate_job_matching(self, matching_result: Dict[str, Any], actual_resume_skills: List[str], job_required_skills: List[str] = None, job_preferred_skills: List[str] = None) -> Dict[str, Any]:
        """Validate job matching results to prevent skill hallucination"""
        if not matching_result:
            return matching_result

        validated_result = matching_result.copy()
        actual_skills_lower = [skill.lower().strip() for skill in actual_resume_skills]

        # Combine all job skills for validation
        all_job_skills = []
        if job_required_skills:
            all_job_skills.extend(job_required_skills)
        if job_preferred_skills:
            all_job_skills.extend(job_preferred_skills)
        job_skills_lower = [skill.lower().strip() for skill in all_job_skills]

        # Validate matching skills
        original_matching = matching_result.get("matching_skills", [])
        validated_matching = []

        for skill in original_matching:
            skill_lower = skill.lower().strip()
            if skill_lower in actual_skills_lower:
                validated_matching.append(skill)
            else:
                # Check for partial matches
                if any(skill_lower in actual_skill or actual_skill in skill_lower
                       for actual_skill in actual_skills_lower):
                    validated_matching.append(skill)
                else:
                    if self.debug_enabled:
                        print(f"🚨 VALIDATION: Removed hallucinated matching skill: '{skill}' (not in actual resume skills)")

        validated_result["matching_skills"] = validated_matching

        # Validate missing skills - they should only be actual technical skills from job requirements
        original_missing = matching_result.get("missing_skills", [])
        validated_missing = []

        # Define what constitutes a valid skill (not experience/education requirements)
        invalid_skill_patterns = [
            r'\d+\+?\s*years?\s*(?:of\s*)?experience',  # "5+ years of experience"
            r'(?:bachelor|master|phd|doctorate).*degree',  # degree requirements
            r'.*experience$',  # anything ending with "experience"
            r'.*platforms?\s*\(',  # "Cloud platforms (AWS, Azure)"
            r'.*and\s+',  # "Docker and containerization"
        ]

        for skill in original_missing:
            skill_lower = skill.lower().strip()

            # Check if this is actually a skill requirement (not experience/education)
            is_valid_skill = True
            for pattern in invalid_skill_patterns:
                if re.search(pattern, skill_lower):
                    is_valid_skill = False
                    if self.debug_enabled:
                        print(f"🚨 VALIDATION: Removed non-skill requirement: '{skill}' (matches pattern: {pattern})")
                    break

            if not is_valid_skill:
                continue

            # Check if this skill is actually from the job description
            if skill_lower in job_skills_lower or any(skill_lower in job_skill or job_skill in skill_lower for job_skill in job_skills_lower):
                # Also check it's not in the resume (should be missing)
                if skill_lower not in actual_skills_lower and not any(skill_lower in actual_skill or actual_skill in skill_lower for actual_skill in actual_skills_lower):
                    validated_missing.append(skill)
                else:
                    if self.debug_enabled:
                        print(f"🚨 VALIDATION: Removed '{skill}' from missing_skills (actually present in resume)")
            else:
                if self.debug_enabled:
                    print(f"🚨 VALIDATION: Removed hallucinated missing skill: '{skill}' (not in job description)")

        validated_result["missing_skills"] = validated_missing

        if self.debug_enabled:
            print(f"🔍 VALIDATION: Actual resume skills: {actual_resume_skills}")
            print(f"🔍 VALIDATION: Job skills: {all_job_skills}")
            print(f"🔍 VALIDATION: Original matching skills: {original_matching}")
            print(f"✅ VALIDATION: Validated matching skills: {validated_matching}")
            print(f"🔍 VALIDATION: Original missing skills: {original_missing}")
            print(f"✅ VALIDATION: Validated missing skills: {validated_missing}")

        return validated_result
    
    def _log_validation_results(self, original: Dict[str, Any], validated: Dict[str, Any]):
        """Log validation results for debugging"""
        changes_made = False
        
        # Check skills changes
        original_skills = set(original.get("skills", []))
        validated_skills = set(validated.get("skills", []))
        removed_skills = original_skills - validated_skills
        
        if removed_skills:
            changes_made = True
            print(f"🔍 VALIDATION: Removed {len(removed_skills)} hallucinated skills: {list(removed_skills)}")
        
        # Check other changes
        for key in ["experience", "education", "certifications"]:
            original_count = len(original.get(key, []))
            validated_count = len(validated.get(key, []))
            if original_count != validated_count:
                changes_made = True
                print(f"🔍 VALIDATION: {key} entries: {original_count} → {validated_count}")
        
        if not changes_made:
            print("✅ VALIDATION: No hallucinations detected - all extracted information is valid")

# Utility function
def validate_ai_response(analysis_result: Dict[str, Any], original_text: str) -> Dict[str, Any]:
    """Quick validation function"""
    validator = ResponseValidator()
    return validator.validate_resume_analysis(analysis_result, original_text)
