{"job_description": "\nSoftware Engineer - Python Developer\n\nWe are looking for a skilled Python Developer to join our team. \n\nRequirements:\n- Bachelor's degree in Computer Science or related field\n- 3+ years of experience in Python development\n- Experience with web frameworks (Django, Flask)\n- Knowledge of databases (PostgreSQL, MySQL)\n- Experience with REST APIs\n- Git version control\n- Problem-solving skills\n\nPreferred:\n- Master's degree\n- 5+ years of experience\n- Experience with cloud platforms (AWS, Azure)\n- Docker and containerization\n- Machine Learning experience\n- Agile development experience\n\nSkills: Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker\n", "processing_stats": {"total_files": 1, "successful_analyses": 1, "failed_analyses": 0, "processing_time": 22.045926332473755, "average_score": 39.6, "top_score": 39.6}, "results": [{"rank": 1, "candidate_name": "AKURATHI SASIDHAR", "file_name": "sasidhar.pdf", "overall_score": 39.6, "skills_match": 7.0, "experience_match": 0.0, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Python, MySQL", "missing_skills": "Git, agile, Flask, azure, aws, docker, rest api, PostgreSQL, Problem-solving skills, machine learning, Django, Docker and containerization, Agile development experience, Cloud platforms (AWS, Azure), Machine Learning experience, 5+ years of experience, Master's degree", "experience_relevance": "Estimated experience level matches 0% of requirements", "recommendations": "Consider developing skills in: Git, agile, Flask, azure, aws; <PERSON>ain more experience to meet the 3+ years requirement"}]}