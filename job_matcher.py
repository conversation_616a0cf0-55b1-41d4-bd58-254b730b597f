"""
Job Description Matcher Module
Handles job description analysis and resume matching logic
"""

import logging
import re
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from ollama_client import OllamaClient
from config import SCORING_WEIGHTS, JOB_ANALYSIS_CONFIG, DEBUG_CONFIG

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class JobRequirements:
    """Data class for job requirements"""
    required_skills: List[str]
    preferred_skills: List[str]
    experience_requirements: Dict[str, Any]
    education_requirements: Dict[str, Any]
    keywords: List[str]
    job_level: str
    industry: str
    raw_description: str

@dataclass
class ResumeProfile:
    """Data class for resume profile"""
    contact_info: Dict[str, str]
    skills: List[str]
    experience: List[Dict[str, str]]
    education: List[Dict[str, str]]
    certifications: List[str]
    summary: str
    raw_text: str

@dataclass
class MatchingResult:
    """Data class for matching results"""
    overall_score: float
    scores: Dict[str, float]
    matching_skills: List[str]
    missing_skills: List[str]
    experience_relevance: str
    recommendations: List[str]
    candidate_name: str
    file_name: str

class JobMatcher:
    """Main class for job matching and scoring"""
    
    def __init__(self, ollama_client: OllamaClient = None):
        self.ollama_client = ollama_client or OllamaClient()
        self.scoring_weights = SCORING_WEIGHTS
        
    def analyze_job_description(self, job_description: str) -> Optional[JobRequirements]:
        """Analyze job description and extract requirements"""
        if not job_description.strip():
            logger.error("❌ Empty job description provided")
            return None
        
        logger.info("🔍 Analyzing job description...")
        
        # Use Ollama for structured analysis
        analysis = self.ollama_client.analyze_job_description(job_description)

        if analysis:
            return JobRequirements(
                required_skills=analysis.get("required_skills", []),
                preferred_skills=analysis.get("preferred_skills", []),
                experience_requirements=analysis.get("experience_requirements", {}),
                education_requirements=analysis.get("education_requirements", {}),
                keywords=analysis.get("keywords", []),
                job_level=analysis.get("job_level", "Unknown"),
                industry=analysis.get("industry", "Unknown"),
                raw_description=job_description
            )
        else:
            # Fallback to basic keyword extraction if Ollama fails
            logger.warning("⚠️ Ollama analysis failed, using fallback method")
            return self._fallback_job_analysis(job_description)
    
    def _fallback_job_analysis(self, job_description: str) -> JobRequirements:
        """Fallback method for job analysis using keyword extraction"""

        # Enhanced skill patterns to catch more skills
        skill_patterns = [
            r'\b(python|java|javascript|c\+\+|c#|php|ruby|go|rust|swift|typescript)\b',
            r'\b(react|angular|vue|django|flask|spring|express|node\.?js|laravel)\b',
            r'\b(sql|mysql|postgresql|mongodb|redis|elasticsearch|sqlite|oracle)\b',
            r'\b(aws|azure|gcp|docker|kubernetes|jenkins|terraform|ansible)\b',
            r'\b(git|github|gitlab|bitbucket|svn)\b',
            r'\b(machine learning|ai|data science|analytics|tensorflow|pytorch)\b',
            r'\b(rest\s*api|graphql|soap|microservices)\b',
            r'\b(html|css|sass|less|bootstrap|tailwind)\b',
            r'\b(agile|scrum|kanban|devops|ci/cd)\b'
        ]

        # Extract skills using regex
        skills = []
        for pattern in skill_patterns:
            matches = re.findall(pattern, job_description.lower(), re.IGNORECASE)
            skills.extend(matches)

        # Additional manual extraction for common missed skills
        job_lower = job_description.lower()

        # Check for database mentions in parentheses
        if 'postgresql' in job_lower and 'mysql' in job_lower:
            if 'postgresql' not in [s.lower() for s in skills]:
                skills.append('PostgreSQL')
            if 'mysql' not in [s.lower() for s in skills]:
                skills.append('MySQL')

        # Check for REST API variations
        if 'rest' in job_lower and 'api' in job_lower:
            if not any('rest' in s.lower() for s in skills):
                skills.append('REST API')

        # Extract experience requirements
        exp_match = re.search(r'(\d+)\+?\s*years?\s*(?:of\s*)?experience', job_description.lower())
        min_years = int(exp_match.group(1)) if exp_match else 0

        # Extract education requirements
        education_match = re.search(r'(bachelor|master|phd|doctorate)', job_description.lower())
        required_degree = education_match.group(1).title() if education_match else ""

        return JobRequirements(
            required_skills=list(set(skills)),
            preferred_skills=[],
            experience_requirements={"min_years": min_years, "preferred_years": min_years + 2, "required_experience": []},
            education_requirements={"required_degree": required_degree, "preferred_degree": "", "fields": []},
            keywords=list(set(skills)),
            job_level="Unknown",
            industry="Unknown",
            raw_description=job_description
        )
    
    def create_resume_profile(self, resume_data: Dict[str, Any], raw_text: str) -> ResumeProfile:
        """Create resume profile from parsed data"""
        return ResumeProfile(
            contact_info=resume_data.get("contact_info", {}),
            skills=resume_data.get("skills", []),
            experience=resume_data.get("experience", []),
            education=resume_data.get("education", []),
            certifications=resume_data.get("certifications", []),
            summary=resume_data.get("summary", ""),
            raw_text=raw_text
        )
    
    def calculate_skills_match(self, resume_skills: List[str], job_requirements: JobRequirements) -> Tuple[float, List[str], List[str]]:
        """Calculate skills matching score"""
        if not job_requirements.required_skills and not job_requirements.preferred_skills:
            return 50.0, [], []
        
        # Normalize skills for comparison
        resume_skills_lower = [skill.lower().strip() for skill in resume_skills]
        required_skills_lower = [skill.lower().strip() for skill in job_requirements.required_skills]
        preferred_skills_lower = [skill.lower().strip() for skill in job_requirements.preferred_skills]
        
        # Find matching skills
        matching_required = []
        matching_preferred = []
        
        for resume_skill in resume_skills_lower:
            # Check for exact matches and partial matches
            for req_skill in required_skills_lower:
                if req_skill in resume_skill or resume_skill in req_skill:
                    matching_required.append(req_skill)
            
            for pref_skill in preferred_skills_lower:
                if pref_skill in resume_skill or resume_skill in pref_skill:
                    matching_preferred.append(pref_skill)
        
        # Remove duplicates
        matching_required = list(set(matching_required))
        matching_preferred = list(set(matching_preferred))
        
        # Calculate score
        total_required = len(job_requirements.required_skills)
        total_preferred = len(job_requirements.preferred_skills)
        
        if total_required == 0 and total_preferred == 0:
            score = 50.0
        else:
            required_score = (len(matching_required) / total_required * 100) if total_required > 0 else 100
            preferred_score = (len(matching_preferred) / total_preferred * 100) if total_preferred > 0 else 100
            
            # Weight required skills more heavily
            score = (required_score * 0.7 + preferred_score * 0.3)
        
        # Find missing skills
        missing_required = [skill for skill in job_requirements.required_skills 
                          if skill.lower() not in [m.lower() for m in matching_required]]
        missing_preferred = [skill for skill in job_requirements.preferred_skills 
                           if skill.lower() not in [m.lower() for m in matching_preferred]]
        
        all_matching = matching_required + matching_preferred
        all_missing = missing_required + missing_preferred
        
        return min(score, 100.0), all_matching, all_missing
    
    def calculate_experience_match(self, resume_experience: List[Dict[str, str]], job_requirements: JobRequirements) -> float:
        """Calculate experience matching score"""
        if not resume_experience:
            return 0.0
        
        min_years_required = job_requirements.experience_requirements.get("min_years", 0)
        
        # Estimate total years of experience
        total_years = 0
        for exp in resume_experience:
            duration = exp.get("duration", "")
            # Simple regex to extract years from duration
            years_match = re.findall(r'(\d+)', duration)
            if years_match:
                # Take the first number as years (rough estimation)
                total_years += int(years_match[0])
        
        # If we can't estimate years, assume some experience exists
        if total_years == 0 and resume_experience:
            total_years = len(resume_experience)  # Rough estimate: 1 year per job
        
        # Calculate score based on years
        if min_years_required == 0:
            return 75.0  # Default score if no specific requirement
        
        if total_years >= min_years_required:
            # Bonus for exceeding requirements
            excess_years = total_years - min_years_required
            score = 85 + min(excess_years * 3, 15)  # Max 100
        else:
            # Penalty for not meeting requirements
            score = (total_years / min_years_required) * 85
        
        return min(score, 100.0)
    
    def calculate_education_match(self, resume_education: List[Dict[str, str]], job_requirements: JobRequirements) -> float:
        """Calculate education matching score"""
        if not resume_education:
            return 30.0  # Some score for having no education info
        
        required_degree = job_requirements.education_requirements.get("required_degree", "").lower()
        
        if not required_degree:
            return 75.0  # Default score if no specific requirement
        
        # Degree hierarchy
        degree_levels = {
            "high school": 1,
            "associate": 2,
            "bachelor": 3,
            "master": 4,
            "phd": 5,
            "doctorate": 5
        }
        
        required_level = degree_levels.get(required_degree, 3)
        
        # Find highest degree in resume
        highest_level = 0
        for edu in resume_education:
            degree = edu.get("degree", "").lower()
            for degree_type, level in degree_levels.items():
                if degree_type in degree:
                    highest_level = max(highest_level, level)
                    break
        
        # Calculate score
        if highest_level >= required_level:
            score = 90 + (highest_level - required_level) * 5  # Bonus for higher degrees
        else:
            score = (highest_level / required_level) * 70
        
        return min(score, 100.0)
    
    def calculate_keywords_match(self, resume_text: str, job_requirements: JobRequirements) -> float:
        """Calculate keyword matching score"""
        if not job_requirements.keywords:
            return 75.0  # Default score if no keywords
        
        resume_text_lower = resume_text.lower()
        matching_keywords = 0
        
        for keyword in job_requirements.keywords:
            if keyword.lower() in resume_text_lower:
                matching_keywords += 1
        
        score = (matching_keywords / len(job_requirements.keywords)) * 100
        return min(score, 100.0)
    
    def match_resume_to_job(self, resume_profile: ResumeProfile, job_requirements: JobRequirements, file_name: str = "") -> MatchingResult:
        """Main method to match resume to job requirements"""
        
        logger.info(f"🎯 Matching resume to job requirements...")
        
        # Calculate individual scores
        skills_score, matching_skills, missing_skills = self.calculate_skills_match(
            resume_profile.skills, job_requirements
        )
        
        experience_score = self.calculate_experience_match(
            resume_profile.experience, job_requirements
        )
        
        education_score = self.calculate_education_match(
            resume_profile.education, job_requirements
        )
        
        keywords_score = self.calculate_keywords_match(
            resume_profile.raw_text, job_requirements
        )
        
        # Use Ollama for overall relevance if available
        overall_relevance_score = 75.0  # Default

        if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_scoring_details", False):
            print(f"\n🎯 DEBUG: Starting Ollama scoring for {resume_profile.contact_info.get('name', 'Unknown')}")

        if self.ollama_client.is_connected:
            try:
                resume_data = {
                    "skills": resume_profile.skills,
                    "experience": resume_profile.experience,
                    "education": resume_profile.education,
                    "summary": resume_profile.summary
                }

                job_data = {
                    "required_skills": job_requirements.required_skills,
                    "preferred_skills": job_requirements.preferred_skills,
                    "experience_requirements": job_requirements.experience_requirements,
                    "education_requirements": job_requirements.education_requirements,
                    "keywords": job_requirements.keywords
                }

                if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_scoring_details", False):
                    print(f"📊 DEBUG: Resume data for scoring:")
                    print(f"   🛠️ Skills: {resume_data['skills']}")
                    print(f"   💼 Experience entries: {len(resume_data['experience'])}")
                    print(f"   🎓 Education entries: {len(resume_data['education'])}")

                    print(f"📋 DEBUG: Job requirements for scoring:")
                    print(f"   🛠️ Required skills: {job_data['required_skills']}")
                    print(f"   ⭐ Preferred skills: {job_data['preferred_skills']}")

                ollama_result = self.ollama_client.score_resume_match(resume_data, job_data)

                if ollama_result:
                    if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_scoring_details", False):
                        print(f"✅ DEBUG: Ollama scoring result: {ollama_result}")
                    overall_relevance_score = ollama_result.get("scores", {}).get("overall_relevance", 75.0)

                    # Update matching and missing skills with Ollama results
                    if "matching_skills" in ollama_result:
                        matching_skills = ollama_result["matching_skills"]
                    if "missing_skills" in ollama_result:
                        missing_skills = ollama_result["missing_skills"]

                    if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_scoring_details", False):
                        print(f"📊 DEBUG: Overall relevance score from Ollama: {overall_relevance_score}")
                        print(f"🔍 DEBUG: Matching skills: {matching_skills}")
                        print(f"🔍 DEBUG: Missing skills: {missing_skills}")
                else:
                    if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_scoring_details", False):
                        print(f"❌ DEBUG: No result from Ollama scoring")

            except Exception as e:
                if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_scoring_details", False):
                    print(f"🚨 DEBUG: Exception in Ollama scoring: {e}")
                logger.warning(f"⚠️ Ollama scoring failed: {e}")
        else:
            if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_scoring_details", False):
                print(f"⚠️ DEBUG: Ollama client not connected, using default score")
        
        # Calculate weighted overall score
        scores = {
            "skills_match": skills_score,
            "experience_match": experience_score,
            "education_match": education_score,
            "keywords_match": keywords_score,
            "overall_relevance": overall_relevance_score
        }
        
        overall_score = sum(
            scores[category] * weight 
            for category, weight in self.scoring_weights.items()
        )
        
        # Generate recommendations
        recommendations = self._generate_recommendations(
            scores, missing_skills, job_requirements
        )
        
        # Get candidate name
        candidate_name = resume_profile.contact_info.get("name", "Unknown")
        
        logger.info(f"✅ Matching completed. Overall score: {overall_score:.1f}")
        
        return MatchingResult(
            overall_score=round(overall_score, 1),
            scores=scores,
            matching_skills=matching_skills,
            missing_skills=missing_skills,
            experience_relevance=f"Estimated experience level matches {experience_score:.0f}% of requirements",
            recommendations=recommendations,
            candidate_name=candidate_name,
            file_name=file_name
        )
    
    def _generate_recommendations(self, scores: Dict[str, float], missing_skills: List[str], job_requirements: JobRequirements) -> List[str]:
        """Generate recommendations for improvement"""
        recommendations = []
        
        if scores["skills_match"] < 70:
            if missing_skills:
                recommendations.append(f"Consider developing skills in: {', '.join(missing_skills[:5])}")
            else:
                recommendations.append("Focus on highlighting relevant technical skills more prominently")
        
        if scores["experience_match"] < 60:
            min_years = job_requirements.experience_requirements.get("min_years", 0)
            if min_years > 0:
                recommendations.append(f"Gain more experience to meet the {min_years}+ years requirement")
            else:
                recommendations.append("Highlight relevant work experience more clearly")
        
        if scores["education_match"] < 70:
            required_degree = job_requirements.education_requirements.get("required_degree", "")
            if required_degree:
                recommendations.append(f"Consider pursuing {required_degree} degree if not already obtained")
        
        if scores["keywords_match"] < 60:
            recommendations.append("Include more industry-specific keywords and terminology")
        
        if not recommendations:
            recommendations.append("Strong candidate profile - consider emphasizing unique strengths")
        
        return recommendations

# Utility functions
def batch_match_resumes(resume_profiles: List[Tuple[ResumeProfile, str]], job_requirements: JobRequirements) -> List[MatchingResult]:
    """Batch process multiple resumes against job requirements"""
    matcher = JobMatcher()
    results = []
    
    logger.info(f"🔄 Processing {len(resume_profiles)} resumes...")
    
    for i, (profile, file_name) in enumerate(resume_profiles, 1):
        logger.info(f"Processing resume {i}/{len(resume_profiles)}: {file_name}")
        result = matcher.match_resume_to_job(profile, job_requirements, file_name)
        results.append(result)
    
    # Sort by overall score (descending)
    results.sort(key=lambda x: x.overall_score, reverse=True)
    
    logger.info("✅ Batch processing completed")
    return results

if __name__ == "__main__":
    # Test the job matcher
    from config import DEFAULT_JOB_DESCRIPTION
    
    matcher = JobMatcher()
    job_req = matcher.analyze_job_description(DEFAULT_JOB_DESCRIPTION)
    
    if job_req:
        print(f"✅ Job analysis successful")
        print(f"Required skills: {job_req.required_skills}")
        print(f"Experience requirement: {job_req.experience_requirements}")
    else:
        print("❌ Job analysis failed")
