#!/usr/bin/env python3
"""
Test PDF Processing Capabilities
"""

import sys
from pathlib import Path
from resume_parser import <PERSON><PERSON><PERSON>ars<PERSON>

def test_pdf_support():
    """Test PDF processing capabilities"""
    print("🔍 Testing PDF Processing Capabilities")
    print("=" * 50)
    
    parser = ResumeParser()
    
    # Check supported formats
    print(f"📋 Supported formats: {parser.supported_formats}")
    print(f"✅ PDF support: {parser.is_supported_format(Path('test.pdf'))}")
    
    # Check if PDF libraries are available
    try:
        import PyPDF2
        print(f"✅ PyPDF2 version: {PyPDF2.__version__}")
    except ImportError:
        print("❌ PyPDF2 not available")
    
    try:
        import pdfplumber
        print(f"✅ pdfplumber available")
    except ImportError:
        print("❌ pdfplumber not available")
    
    # Test with existing resume files
    resumes_dir = Path("resumes")
    if resumes_dir.exists():
        pdf_files = list(resumes_dir.glob("*.pdf"))
        print(f"\n📄 Found {len(pdf_files)} PDF files in resumes directory:")
        for pdf_file in pdf_files:
            print(f"   • {pdf_file.name}")
        
        if pdf_files:
            print(f"\n🧪 Testing PDF parsing with {pdf_files[0].name}...")
            result = parser.parse_resume(pdf_files[0])
            
            if result["success"]:
                print(f"✅ Successfully parsed PDF!")
                print(f"   📊 Word count: {result['word_count']}")
                print(f"   📄 File size: {result['file_size']} bytes")
                print(f"   📝 First 200 characters:")
                print(f"   {result['text'][:200]}...")
            else:
                print(f"❌ Failed to parse PDF: {result['error']}")
        else:
            print("💡 No PDF files found. Add PDF resumes to the 'resumes' folder to test.")
    else:
        print("❌ Resumes directory not found")
    
    print(f"\n📋 PDF Processing Instructions:")
    print(f"1. Place PDF resume files in the 'resumes/' folder")
    print(f"2. Run: python main.py --batch")
    print(f"3. Check results in the 'output/' folder")
    
    print(f"\n🔧 PDF Compatibility:")
    print(f"✅ Text-based PDFs: Fully supported")
    print(f"✅ Multi-page PDFs: Fully supported") 
    print(f"✅ Complex layouts: Supported via pdfplumber")
    print(f"⚠️ Image-only PDFs: Limited support (OCR not included)")
    print(f"⚠️ Password-protected PDFs: Not supported")

if __name__ == "__main__":
    test_pdf_support()
