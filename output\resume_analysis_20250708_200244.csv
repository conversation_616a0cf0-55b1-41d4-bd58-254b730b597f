rank,candidate_name,file_name,overall_score,skills_match,experience_match,education_match,keywords_match,overall_relevance,matching_skills,missing_skills,experience_relevance,recommendations
1,<PERSON>id<PERSON><PERSON><PERSON>,Profile_9.pdf,68.2,19.166666666666664,100.0,90.0,75.0,82,"Django, Git","Python, REST API, PostgreSQL, MySQL, AWS, Azure, Docker, Machine Learning",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, REST API, PostgreSQL, MySQL, AWS"
2,<PERSON><PERSON> Nanaware,Profile_12.pdf,68.0,15.0,100.0,95.0,75.0,82,"Python, Django, PostgreSQL","AWS, Azure, Docker, Machine Learning",Estimated experience level matches 100% of requirements,"Consider developing skills in: AWS, Azure, Docker, Machine Learning"
3,<PERSON><PERSON><PERSON>,Profile_8.pdf,64.7,7.5,100.0,90.0,75.0,82,"Java, Backend","Python, Django, PostgreSQL, MySQL, REST API, Git, AWS, Azure, Docker, Machine Learning",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, MySQL, REST API"
4,Preksha <PERSON>,Profile_1.pdf,62.5,0.0,100.0,90.0,75.0,82,"LinkedIn Strategy, Brand Strategy","Python, Django, PostgreSQL, MySQL, REST API, Git, AWS, Azure, Docker, Machine Learning",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, MySQL, REST API"
5,Nisarg Gogate,Profile_10.pdf,62.5,0.0,100.0,90.0,75.0,82,"Python, Django","PostgreSQL, MySQL, REST API, Git, AWS, Azure, Docker, Machine Learning",Estimated experience level matches 100% of requirements,"Consider developing skills in: PostgreSQL, MySQL, REST API, Git, AWS"
6,Vijay Komarapu,Profile_14.pdf,62.5,0.0,100.0,90.0,75.0,82,"Python, Django, PostgreSQL","AWS, Azure, Docker, Machine Learning",Estimated experience level matches 100% of requirements,"Consider developing skills in: AWS, Azure, Docker, Machine Learning"
7,Raghavendra (Raghu) Uppalli,Profile_13.pdf,62.5,0.0,100.0,90.0,75.0,82,"Python, Django, PostgreSQL, Git","AWS, Azure, Docker, Machine Learning",Estimated experience level matches 100% of requirements,"Consider developing skills in: AWS, Azure, Docker, Machine Learning"
8,Vasu Beri,Profile_2.pdf,62.5,0.0,100.0,90.0,75.0,82,"Python, Django, PostgreSQL","AWS, Azure, Docker, Machine Learning",Estimated experience level matches 100% of requirements,"Consider developing skills in: AWS, Azure, Docker, Machine Learning"
9,Kartick Kumar Nayagam,Profile_18.pdf,62.5,0.0,100.0,90.0,75.0,82,"Python, Django, PostgreSQL","AWS, Azure, Docker, Machine Learning",Estimated experience level matches 100% of requirements,"Consider developing skills in: AWS, Azure, Docker, Machine Learning"
10,Sanika Jain,Profile_5.pdf,62.5,0.0,100.0,90.0,75.0,82,"Python, Django, PostgreSQL","AWS, Azure, Docker, Machine Learning",Estimated experience level matches 100% of requirements,"Consider developing skills in: AWS, Azure, Docker, Machine Learning"
11,Priyansh Agarwal,Profile_7.pdf,62.5,0.0,100.0,90.0,75.0,82,"Python, Django, PostgreSQL","MySQL, REST API, Git, AWS, Azure, Docker, Machine Learning",Estimated experience level matches 100% of requirements,"Consider developing skills in: MySQL, REST API, Git, AWS, Azure"
12,Dharinee Gupta,Profile_17.pdf,59.2,0.0,100.0,90.0,75.0,50,,"Python, Django, PostgreSQL, MySQL, REST API, Git",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, MySQL, REST API"
13,Arindam Mukherjee,Profile_19.pdf,59.2,0.0,100.0,90.0,75.0,50,,,Estimated experience level matches 100% of requirements,Focus on highlighting relevant technical skills more prominently
14,Deepa Sajjanshetty,Profile_4.pdf,59.2,0.0,100.0,95.0,75.0,40,,"Python, Django, PostgreSQL, MySQL, REST API, Git",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, MySQL, REST API"
15,Arsh Goyal,Profile_6.pdf,59.2,0.0,100.0,90.0,75.0,50,,,Estimated experience level matches 100% of requirements,Focus on highlighting relevant technical skills more prominently
16,AKURATHI SASIDHAR,sasidhar.pdf,55.4,23.33333333333333,56.666666666666664,90.0,75.0,50,,,Estimated experience level matches 57% of requirements,Focus on highlighting relevant technical skills more prominently; Gain more experience to meet the 3+ years requirement
17,Kaviya E.,Profile_21.pdf,55.2,0.0,100.0,95.0,75.0,0,,"Python, Django, PostgreSQL, MySQL, REST API, Git",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, MySQL, REST API"
18,Suyash Gaidhani,Profile_15.pdf,44.5,0.0,100.0,0.0,75.0,82,"Python, Django, PostgreSQL","AWS, Azure, Docker, Machine Learning",Estimated experience level matches 100% of requirements,"Consider developing skills in: AWS, Azure, Docker, Machine Learning; Consider pursuing Bachelor's degree if not already obtained"
19,Fahad Ibraheem,Profile_3.pdf,44.5,0.0,100.0,0.0,75.0,82,"vector tracing, image resize and crop","Python, Django, PostgreSQL, MySQL, REST API, Git, AWS, Azure, Docker, Machine Learning",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, MySQL, REST API; Consider pursuing Bachelor's degree if not already obtained"
20,Unknown,Profile_11.pdf,25.5,0.0,0.0,30.0,75.0,82,"Python, Django, PostgreSQL","AWS, Azure, Docker, Machine Learning",Estimated experience level matches 0% of requirements,"Consider developing skills in: AWS, Azure, Docker, Machine Learning; Gain more experience to meet the 3+ years requirement; Consider pursuing Bachelor's degree if not already obtained"
21,Unknown,Profile_16.pdf,25.5,0.0,0.0,30.0,75.0,82,"Python, Django, PostgreSQL","AWS, Azure, Docker, Machine Learning",Estimated experience level matches 0% of requirements,"Consider developing skills in: AWS, Azure, Docker, Machine Learning; Gain more experience to meet the 3+ years requirement; Consider pursuing Bachelor's degree if not already obtained"
22,Unknown,Profile_20.pdf,25.5,0.0,0.0,30.0,75.0,82,"Python, Django, PostgreSQL","AWS, Azure, Docker, Machine Learning",Estimated experience level matches 0% of requirements,"Consider developing skills in: AWS, Azure, Docker, Machine Learning; Gain more experience to meet the 3+ years requirement; Consider pursuing Bachelor's degree if not already obtained"
