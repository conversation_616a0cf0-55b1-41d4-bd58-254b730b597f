rank,candidate_name,file_name,overall_score,skills_match,experience_match,education_match,keywords_match,overall_relevance,matching_skills,missing_skills,experience_relevance,recommendations
1,AKURATHI SASIDHAR,sasidhar.pdf,58.6,23.33333333333333,56.666666666666664,90,75.0,82,"Python, MySQL","Django, PostgreSQL, REST API, Git, AWS, Azure, Docker, Machine Learning",Estimated experience level matches 57% of requirements,"Consider developing skills in: Django, PostgreSQL, REST API, Git, AWS; Gain more experience to meet the 3+ years requirement"
