# Bulk Resume Parser and Scoring System

An intelligent resume screening and candidate ranking system powered by Ollama LLM, optimized for local processing on your hardware.

## 🎯 Features

- **Multi-format Support**: Parse PDF, DOCX, DOC, and TXT resume files
- **AI-Powered Analysis**: Uses Ollama LLM for intelligent resume analysis and scoring
- **Bulk Processing**: Process hundreds of resumes efficiently with parallel processing
- **Comprehensive Scoring**: Multi-criteria scoring with customizable weights
- **Detailed Reports**: Generate executive summaries, CSV exports, and candidate profiles
- **Local Processing**: Complete privacy - all processing happens on your machine
- **Hardware Optimized**: Configured for Intel i5 13450hx, 24GB RAM, RTX 4050

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Clone or download the project
cd "Test Resume Parser"

# Run the setup script
python setup_environment.py
```

### 2. Install Ollama

1. Download Ollama from [https://ollama.ai/](https://ollama.ai/)
2. Install and start Ollama
3. Pull a model (recommended):
   ```bash
   ollama pull llama2
   # OR for better performance:
   ollama pull mistral
   ```

### 3. Add Resume Files

Place resume files (PDF, DOCX, TXT) in the `resumes/` directory:
```
resumes/
├── john_doe_resume.pdf
├── jane_smith_resume.docx
├── mike_johnson_resume.txt
└── ...
```

### 4. Run the System

**Interactive Mode:**
```bash
python main.py
```

**Batch Mode:**
```bash
python main.py --batch
```

**With Custom Job Description:**
```bash
python main.py --batch --job-file job_description.txt
```

**With Debug Output (see Ollama responses):**
```bash
python run_with_debug.py --batch
```

**Clean Output (no debug):**
```bash
python run_clean.py --batch
```

## 📁 Project Structure

```
├── main.py                 # Main application entry point
├── config.py              # Configuration settings
├── resume_parser.py       # Resume text extraction
├── ollama_client.py       # Ollama LLM integration
├── job_matcher.py         # Job matching and scoring logic
├── bulk_processor.py      # Batch processing engine
├── scoring_system.py      # Advanced scoring algorithms
├── report_generator.py    # Report generation
├── test_suite.py          # Comprehensive test suite
├── setup_environment.py   # Environment setup script
├── requirements.txt       # Python dependencies
├── resumes/              # Directory for resume files
├── output/               # Generated reports and results
└── temp/                 # Temporary processing files
```

## ⚙️ Configuration

Edit `config.py` to customize:

- **Ollama Settings**: Model selection, server URL, timeout
- **Scoring Weights**: Adjust importance of different criteria
- **Performance Settings**: Batch size, parallel workers
- **Output Formats**: CSV, JSON, report types

### Scoring Criteria

Default scoring weights:
- Skills Match: 30%
- Experience Match: 25%
- Education Match: 20%
- Keywords Match: 15%
- Overall Relevance: 10%

## 📊 Output Reports

The system generates multiple report types:

### 1. Executive Summary
- Key metrics and statistics
- Top candidates overview
- Hiring recommendations
- Candidate pool analysis

### 2. Detailed CSV Report
- Complete scoring breakdown
- Skills analysis
- Recommendations for each candidate

### 3. JSON Export
- Machine-readable complete data
- API integration ready
- Custom analysis support

### 4. Individual Candidate Profiles
- Detailed analysis per candidate
- Specific recommendations
- Score explanations

## 🧪 Testing

Run the comprehensive test suite:

```bash
python test_suite.py
```

Test individual components:
```bash
# Test resume parsing
python resume_parser.py

# Test Ollama connection
python ollama_client.py

# Test job matching
python job_matcher.py
```

## 🔍 Debugging and Development

### **Debug Mode**
See exactly what Ollama is returning and how the system processes resumes:

```bash
# Run with full debug output
python run_with_debug.py --batch

# Or enable specific debug options in config.py
DEBUG_CONFIG = {
    "enabled": True,
    "show_ollama_responses": True,    # See raw AI responses
    "show_json_parsing": True,        # See JSON parsing details
    "show_scoring_details": True,     # See scoring process
    "show_resume_analysis": True,     # See analysis steps
}
```

### **Debug Output Examples**
When debug mode is enabled, you'll see:

```
🤖 DEBUG: Raw Ollama Response:
📝 Response length: 1590 characters
📄 Raw response preview: {
  "contact_info": {
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "skills": ["Python", "Django", "PostgreSQL"]
  ...
}

🔍 DEBUG: JSON boundaries - start: 51, end: 1590
✅ DEBUG: Successfully parsed JSON with keys: ['contact_info', 'skills', 'experience']

🎯 DEBUG: Starting Ollama scoring for John Doe
📊 DEBUG: Resume data for scoring:
   🛠️ Skills: ['Python', 'Django', 'PostgreSQL']
   💼 Experience entries: 3
```

### **Testing Tools**
```bash
# Interactive debug testing
python test_debug.py

# Test specific components
python test_suite.py

# Test PDF processing
python test_pdf_processing.py
```

## 🔧 Troubleshooting

### Common Issues

**1. Ollama Connection Failed**
```bash
# Check if Ollama is running
ollama --version

# Start Ollama server
ollama serve

# Pull required model
ollama pull llama2
```

**2. No Resume Files Found**
```bash
# Create sample data
python main.py --setup-samples

# Check directory
ls resumes/
```

**3. Memory Issues**
- Reduce batch size in `config.py`
- Close other applications
- Use smaller Ollama model

**4. Slow Processing**
- Increase parallel workers (if CPU allows)
- Use faster Ollama model
- Enable GPU acceleration (if supported)

### Performance Optimization

For your hardware (Intel i5 13450hx, 24GB RAM, RTX 4050):

```python
# In config.py
PERFORMANCE_CONFIG = {
    "batch_size": 5,        # Process 5 resumes at once
    "max_workers": 4,       # Use 4 CPU cores
    "use_gpu": True,        # Enable GPU if supported
    "memory_limit_gb": 20,  # Leave 4GB for system
}
```

## 📈 Usage Examples

### Basic Usage
```python
from bulk_processor import BulkProcessor
from config import DEFAULT_JOB_DESCRIPTION

processor = BulkProcessor()
results, stats = processor.process_all_resumes(DEFAULT_JOB_DESCRIPTION)

print(f"Processed {stats.total_files} resumes")
print(f"Top candidate: {results[0].candidate_name} ({results[0].overall_score:.1f})")
```

### Custom Scoring
```python
from scoring_system import AdvancedScoringSystem

# Custom weights
custom_weights = {
    "skills_match": 0.40,      # Emphasize skills more
    "experience_match": 0.30,
    "education_match": 0.10,   # De-emphasize education
    "keywords_match": 0.10,
    "overall_relevance": 0.10
}

scoring_system = AdvancedScoringSystem(custom_weights)
ranked_candidates = scoring_system.rank_candidates(results, custom_weights)
```

### Generate Custom Reports
```python
from report_generator import ReportGenerator

generator = ReportGenerator()
report_files = generator.generate_comprehensive_report(
    results, job_requirements, stats, job_description
)

print("Generated reports:")
for report_type, file_path in report_files.items():
    print(f"  {report_type}: {file_path}")
```

## 🔒 Privacy & Security

- **Local Processing**: All data stays on your machine
- **No Cloud Dependencies**: Uses local Ollama LLM
- **Secure File Handling**: Temporary files are cleaned up
- **No Data Transmission**: No resume data sent to external servers

## 🛠️ Development

### Adding New Features

1. **New File Format Support**: Extend `resume_parser.py`
2. **Custom Scoring Criteria**: Modify `job_matcher.py`
3. **Additional Report Types**: Extend `report_generator.py`
4. **New LLM Models**: Update `ollama_client.py`

### Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Run test suite
5. Submit pull request

## 📋 Requirements

- Python 3.8+
- 8GB+ RAM (24GB recommended)
- Ollama installed and running
- Supported OS: Windows, macOS, Linux

## 📄 License

This project is open source. Feel free to use and modify for your needs.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Run system check: `python main.py --check-system`
3. Review logs in `resume_parser.log`
4. Test individual components

---

**Optimized for your hardware**: Intel i5 13450hx, 24GB RAM, RTX 4050 Mobile
**Perfect for**: HR departments, recruitment agencies, hiring managers
