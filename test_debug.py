#!/usr/bin/env python3
"""
Test Debug Functionality
Run this to see detailed Ollama responses and processing steps
"""

import sys
from pathlib import Path

# Ensure we can import our modules
sys.path.insert(0, str(Path(__file__).parent))

def test_debug_mode():
    """Test the debug functionality with a single resume"""
    print("🔍 Testing Debug Mode - Ollama Response Debugging")
    print("=" * 60)
    
    # Import after setting path
    from bulk_processor import BulkProcessor
    from config import DEFAULT_JOB_DESCRIPTION, DEBUG_CONFIG
    
    # Show current debug settings
    print("📋 Current Debug Settings:")
    for key, value in DEBUG_CONFIG.items():
        status = "✅ Enabled" if value else "❌ Disabled"
        print(f"   {key}: {status}")
    
    print(f"\n🎯 Processing resumes with debug output...")
    print("=" * 60)
    
    # Initialize processor
    processor = BulkProcessor()
    
    # Check if we have resumes to process
    resume_files = processor.find_resume_files()
    if not resume_files:
        print("❌ No resume files found. Please add some resumes to the 'resumes' folder.")
        return
    
    print(f"📄 Found {len(resume_files)} resume files:")
    for file in resume_files:
        print(f"   • {file.name}")
    
    # Process with debug output
    try:
        results, stats = processor.process_all_resumes(DEFAULT_JOB_DESCRIPTION)
        
        print(f"\n✅ Processing completed!")
        print(f"📊 Results: {len(results)} resumes processed")
        print(f"⏱️ Time: {stats.processing_time:.2f} seconds")
        
        if results:
            print(f"\n🏆 Top candidate: {results[0].candidate_name} ({results[0].overall_score:.1f}/100)")
        
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()

def toggle_debug_settings():
    """Toggle debug settings interactively"""
    print("🔧 Debug Settings Configuration")
    print("=" * 40)
    
    from config import DEBUG_CONFIG
    
    print("Current settings:")
    for i, (key, value) in enumerate(DEBUG_CONFIG.items(), 1):
        status = "✅ ON" if value else "❌ OFF"
        print(f"{i}. {key}: {status}")
    
    print("\nTo toggle a setting, enter its number (1-5), or 'q' to quit:")
    
    while True:
        choice = input("Enter choice: ").strip().lower()
        
        if choice == 'q':
            break
        
        try:
            choice_num = int(choice)
            if 1 <= choice_num <= len(DEBUG_CONFIG):
                keys = list(DEBUG_CONFIG.keys())
                key = keys[choice_num - 1]
                DEBUG_CONFIG[key] = not DEBUG_CONFIG[key]
                status = "✅ ON" if DEBUG_CONFIG[key] else "❌ OFF"
                print(f"Toggled {key}: {status}")
            else:
                print("Invalid choice. Please enter 1-5 or 'q'.")
        except ValueError:
            print("Invalid input. Please enter a number or 'q'.")

def main():
    """Main function"""
    print("🧪 OLLAMA DEBUG TESTING TOOL")
    print("=" * 50)
    
    print("Choose an option:")
    print("1. Run debug test with current settings")
    print("2. Configure debug settings")
    print("3. Run test with all debug enabled")
    print("4. Run test with debug disabled")
    print("q. Quit")
    
    while True:
        choice = input("\nEnter choice (1-4, q): ").strip().lower()
        
        if choice == 'q':
            print("👋 Goodbye!")
            break
        elif choice == '1':
            test_debug_mode()
            break
        elif choice == '2':
            toggle_debug_settings()
            test_debug_mode()
            break
        elif choice == '3':
            # Enable all debug options
            from config import DEBUG_CONFIG
            for key in DEBUG_CONFIG:
                DEBUG_CONFIG[key] = True
            print("✅ All debug options enabled")
            test_debug_mode()
            break
        elif choice == '4':
            # Disable all debug options
            from config import DEBUG_CONFIG
            for key in DEBUG_CONFIG:
                if key != 'enabled':  # Keep main enabled flag
                    DEBUG_CONFIG[key] = False
            print("❌ Debug output minimized")
            test_debug_mode()
            break
        else:
            print("Invalid choice. Please enter 1-4 or 'q'.")

if __name__ == "__main__":
    main()
