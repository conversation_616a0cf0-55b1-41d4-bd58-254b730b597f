rank,candidate_name,file_name,overall_score,skills_match,experience_match,education_match,keywords_match,overall_relevance,matching_skills,missing_skills,experience_relevance,recommendations
1,AKURATHI SASIDHAR,sasidhar.pdf,37.2,11.666666666666664,0.0,90,50.0,82,"Python, JSON conversion","Django, PostgreSQL, REST API, Git, MySQL, Data Structures, Object-oriented programming",Estimated experience level matches 0% of requirements,"Consider developing skills in: Django, PostgreSQL, REST API, Git, MySQL; Gain more experience to meet the 3+ years requirement; Include more industry-specific keywords and terminology"
