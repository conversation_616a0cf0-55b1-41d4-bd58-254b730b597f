{"job_description": "\nSoftware Engineer - Python Developer\n\nWe are looking for a skilled Python Developer to join our team. \n\nRequirements:\n- Bachelor's degree in Computer Science or related field\n- 3+ years of experience in Python development\n- Experience with web frameworks (Django, Flask)\n- Knowledge of databases (PostgreSQL, MySQL)\n- Experience with REST APIs\n- Git version control\n- Problem-solving skills\n\nPreferred:\n- Master's degree\n- 5+ years of experience\n- Experience with cloud platforms (AWS, Azure)\n- Docker and containerization\n- Machine Learning experience\n- Agile development experience\n\nSkills: Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker\n", "processing_stats": {"total_files": 1, "successful_analyses": 1, "failed_analyses": 0, "processing_time": 17.52922773361206, "average_score": 37.2, "top_score": 37.2}, "results": [{"rank": 1, "candidate_name": "AKURATHI SASIDHAR", "file_name": "sasidhar.pdf", "overall_score": 37.2, "skills_match": 11.666666666666664, "experience_match": 0.0, "education_match": 90, "keywords_match": 50.0, "overall_relevance": 82, "matching_skills": "Python, JSON conversion", "missing_skills": "Django, PostgreSQL, REST API, Git, MySQL, Data Structures, Object-oriented programming", "experience_relevance": "Estimated experience level matches 0% of requirements", "recommendations": "Consider developing skills in: Django, PostgreSQL, REST API, Git, MySQL; Gain more experience to meet the 3+ years requirement; Include more industry-specific keywords and terminology"}]}