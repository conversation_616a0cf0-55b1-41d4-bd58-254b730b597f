"""
Configuration settings for Resume Parser
"""

import os
from pathlib import Path
from typing import Dict, List

# Project paths
PROJECT_ROOT = Path(__file__).parent
RESUMES_DIR = PROJECT_ROOT / "resumes"
OUTPUT_DIR = PROJECT_ROOT / "output"
TEMP_DIR = PROJECT_ROOT / "temp"

# Create directories if they don't exist
for directory in [RESUMES_DIR, OUTPUT_DIR, TEMP_DIR]:
    directory.mkdir(exist_ok=True)

# Ollama configuration
OLLAMA_CONFIG = {
    "base_url": "http://localhost:11434",
    "model": "llama3.2:3b",  # Using the available model
    "timeout": 120,  # seconds
    "temperature": 0.1,  # Lower temperature for more consistent results
}

# Supported file formats
SUPPORTED_FORMATS = [".pdf", ".docx", ".doc", ".txt"]

# Scoring weights (adjust based on your requirements)
SCORING_WEIGHTS = {
    "skills_match": 0.30,
    "experience_match": 0.25,
    "education_match": 0.20,
    "keywords_match": 0.15,
    "overall_relevance": 0.10,
}

# Resume parsing configuration
PARSING_CONFIG = {
    "max_file_size_mb": 10,
    "extract_contact_info": True,
    "extract_skills": True,
    "extract_experience": True,
    "extract_education": True,
    "extract_certifications": True,
}

# Job description analysis configuration
JOB_ANALYSIS_CONFIG = {
    "extract_required_skills": True,
    "extract_preferred_skills": True,
    "extract_experience_requirements": True,
    "extract_education_requirements": True,
    "extract_keywords": True,
}

# Output configuration
OUTPUT_CONFIG = {
    "export_formats": ["csv", "json"],
    "include_full_text": False,  # Set to True if you want full resume text in output
    "max_summary_length": 500,
    "sort_by_score": True,
}

# Logging configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "resume_parser.log",
}

# Debug configuration
DEBUG_CONFIG = {
    "enabled": True,  # Set to False to disable debug output
    "show_ollama_responses": True,  # Show raw Ollama responses
    "show_json_parsing": True,  # Show JSON parsing details
    "show_scoring_details": True,  # Show detailed scoring process
    "show_resume_analysis": True,  # Show resume analysis details
}

# Performance settings (optimized for your hardware)
PERFORMANCE_CONFIG = {
    "batch_size": 5,  # Process 5 resumes at a time
    "max_workers": 4,  # Parallel processing threads
    "use_gpu": True,  # Enable if Ollama supports GPU acceleration
    "memory_limit_gb": 20,  # Leave 4GB for system
}

# Prompt templates for Ollama
PROMPT_TEMPLATES = {
    "resume_analysis": """
CRITICAL INSTRUCTION: Extract ONLY information that is explicitly mentioned in the resume text. Do NOT infer, assume, or add any information that is not directly stated.

RESUME TEXT:
{resume_text}

Extract structured information following these strict rules:
1. ONLY extract skills that are explicitly mentioned in the resume text
2. ONLY extract experience that is clearly stated
3. ONLY extract education that is directly mentioned
4. If information is not present, use empty strings or empty arrays
5. Do NOT make assumptions based on job titles or company names
6. Do NOT add skills that seem relevant but are not mentioned

Provide a JSON response with this exact structure:
{{
    "contact_info": {{
        "name": "Full Name from resume or empty string",
        "email": "<EMAIL> or empty string",
        "phone": "phone number or empty string",
        "location": "city, state/country or empty string"
    }},
    "skills": ["only skills explicitly mentioned in resume text"],
    "experience": [
        {{
            "title": "Exact job title from resume",
            "company": "Exact company name from resume",
            "duration": "Exact duration from resume",
            "description": "Brief description from resume"
        }}
    ],
    "education": [
        {{
            "degree": "Exact degree name from resume",
            "institution": "Exact institution name from resume",
            "year": "Exact year from resume"
        }}
    ],
    "certifications": ["only certifications explicitly mentioned"],
    "summary": "Brief summary based only on information present in resume"
}}

IMPORTANT: Respond only with valid JSON. Do not add explanatory text.
""",

    "job_matching": """
Compare this resume against the job description and provide a detailed scoring:

JOB DESCRIPTION:
{job_description}

RESUME SUMMARY:
{resume_summary}

RESUME SKILLS: {resume_skills}
RESUME EXPERIENCE: {resume_experience}

Please provide a JSON response with scoring (0-100 for each category):
{{
    "scores": {{
        "skills_match": 85,
        "experience_match": 75,
        "education_match": 90,
        "keywords_match": 80,
        "overall_relevance": 82
    }},
    "matching_skills": ["skill1", "skill2"],
    "missing_skills": ["skill3", "skill4"],
    "experience_relevance": "Brief explanation of experience match",
    "recommendations": "Suggestions for improvement",
    "overall_score": 82
}}

Respond only with valid JSON.
""",

    "job_analysis": """
Analyze this job description and extract key requirements:

JOB DESCRIPTION:
{job_description}

IMPORTANT: Only extract technical skills, programming languages, frameworks, databases, and tools.
Do NOT include experience requirements, education requirements, or soft skills in the skills lists.

Please provide a JSON response with:
{{
    "required_skills": ["Python", "Django", "PostgreSQL"],
    "preferred_skills": ["AWS", "Docker"],
    "experience_requirements": {{
        "min_years": 3,
        "preferred_years": 5,
        "required_experience": ["web development", "backend development"]
    }},
    "education_requirements": {{
        "required_degree": "Bachelor's",
        "preferred_degree": "Master's",
        "fields": ["Computer Science", "Engineering"]
    }},
    "keywords": ["keyword1", "keyword2"],
    "job_level": "Mid-level",
    "industry": "Technology"
}}

Respond only with valid JSON.
"""
}

# Default job description (for testing)
DEFAULT_JOB_DESCRIPTION = """
Software Engineer - Python Developer

We are looking for a skilled Python Developer to join our team.

Required Skills:
- Python
- Django
- Flask
- PostgreSQL
- MySQL
- REST API
- Git

Preferred Skills:
- AWS
- Azure
- Docker
- Machine Learning
- Agile

Requirements:
- Bachelor's degree in Computer Science or related field
- 3+ years of experience in Python development
- Experience with web frameworks
- Knowledge of databases
- Problem-solving skills

Preferred:
- Master's degree
- 5+ years of experience
- Experience with cloud platforms
- Docker and containerization experience
- Machine Learning experience
- Agile development experience
"""
