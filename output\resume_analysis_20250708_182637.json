{"job_description": "\nSoftware Engineer - Python Developer\n\nWe are looking for a skilled Python Developer to join our team. \n\nRequirements:\n- Bachelor's degree in Computer Science or related field\n- 3+ years of experience in Python development\n- Experience with web frameworks (Django, Flask)\n- Knowledge of databases (PostgreSQL, MySQL)\n- Experience with REST APIs\n- Git version control\n- Problem-solving skills\n\nPreferred:\n- Master's degree\n- 5+ years of experience\n- Experience with cloud platforms (AWS, Azure)\n- Docker and containerization\n- Machine Learning experience\n- Agile development experience\n\nSkills: Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker\n", "processing_stats": {"total_files": 21, "successful_analyses": 21, "failed_analyses": 0, "processing_time": 259.80690002441406, "average_score": 48.87142857142857, "top_score": 68.0}, "results": [{"rank": 1, "candidate_name": "Aishwarya Mahapatra", "file_name": "Profile_11.pdf", "overall_score": 68.0, "skills_match": 30.83333333333333, "experience_match": 100, "education_match": 90, "keywords_match": 50.0, "overall_relevance": 82, "matching_skills": "git, python, machine learning", "missing_skills": "Django, Flask, PostgreSQL, REST API, AWS, Docker, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Django, Flask, PostgreSQL, REST API, AWS; Include more industry-specific keywords and terminology"}, {"rank": 2, "candidate_name": "Vidhyasagar P", "file_name": "Profile_9.pdf", "overall_score": 62.2, "skills_match": 11.666666666666664, "experience_match": 100, "education_match": 90, "keywords_match": 50.0, "overall_relevance": 82, "matching_skills": "django", "missing_skills": "Python, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Flask, PostgreSQL, REST API, Git; Include more industry-specific keywords and terminology"}, {"rank": 3, "candidate_name": "<PERSON><PERSON>", "file_name": "Profile_12.pdf", "overall_score": 56.7, "skills_match": 15.0, "experience_match": 100, "education_match": 95, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "docker, machine learning", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"}, {"rank": 4, "candidate_name": "<PERSON>", "file_name": "Profile_14.pdf", "overall_score": 55.7, "skills_match": 15.0, "experience_match": 100, "education_match": 90, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "docker, machine learning", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"}, {"rank": 5, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_8.pdf", "overall_score": 53.5, "skills_match": 7.5, "experience_match": 100, "education_match": 90, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "aws", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"}, {"rank": 6, "candidate_name": "Ka<PERSON>ya E.", "file_name": "Profile_21.pdf", "overall_score": 52.2, "skills_match": 0.0, "experience_match": 100, "education_match": 95, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"}, {"rank": 7, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_20.pdf", "overall_score": 52.2, "skills_match": 0.0, "experience_match": 100, "education_match": 95, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"}, {"rank": 8, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_10.pdf", "overall_score": 51.2, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"}, {"rank": 9, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_1.pdf", "overall_score": 51.2, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"}, {"rank": 10, "candidate_name": "<PERSON><PERSON><PERSON><PERSON> (Raghu) Uppalli", "file_name": "Profile_13.pdf", "overall_score": 51.2, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"}, {"rank": 11, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_17.pdf", "overall_score": 51.2, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"}, {"rank": 12, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_18.pdf", "overall_score": 51.2, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"}, {"rank": 13, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_19.pdf", "overall_score": 51.2, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"}, {"rank": 14, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_2.pdf", "overall_score": 51.2, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"}, {"rank": 15, "candidate_name": "<PERSON><PERSON>", "file_name": "Profile_5.pdf", "overall_score": 51.2, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"}, {"rank": 16, "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "file_name": "Profile_7.pdf", "overall_score": 51.2, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"}, {"rank": 17, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_6.pdf", "overall_score": 51.2, "skills_match": 0.0, "experience_match": 100, "education_match": 90, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"}, {"rank": 18, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_15.pdf", "overall_score": 33.2, "skills_match": 0.0, "experience_match": 100, "education_match": 0.0, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Consider pursuing Bachelor's degree if not already obtained; Include more industry-specific keywords and terminology"}, {"rank": 19, "candidate_name": "<PERSON>", "file_name": "Profile_16.pdf", "overall_score": 33.2, "skills_match": 0.0, "experience_match": 100, "education_match": 0.0, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Consider pursuing Bachelor's degree if not already obtained; Include more industry-specific keywords and terminology"}, {"rank": 20, "candidate_name": "<PERSON><PERSON><PERSON>", "file_name": "Profile_3.pdf", "overall_score": 33.2, "skills_match": 0.0, "experience_match": 100, "education_match": 0.0, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 100% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Consider pursuing Bachelor's degree if not already obtained; Include more industry-specific keywords and terminology"}, {"rank": 21, "candidate_name": "Unknown", "file_name": "Profile_4.pdf", "overall_score": 14.2, "skills_match": 0.0, "experience_match": 0.0, "education_match": 30.0, "keywords_match": 0.0, "overall_relevance": 82, "matching_skills": "", "missing_skills": "Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development", "experience_relevance": "Estimated experience level matches 0% of requirements", "recommendations": "Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Gain more experience to meet the 3+ years requirement; Consider pursuing Bachelor's degree if not already obtained; Include more industry-specific keywords and terminology"}]}