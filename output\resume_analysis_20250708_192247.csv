rank,candidate_name,file_name,overall_score,skills_match,experience_match,education_match,keywords_match,overall_relevance,matching_skills,missing_skills,experience_relevance,recommendations
1,<PERSON><PERSON><PERSON><PERSON>,Profile_11.pdf,68.0,30.83333333333333,100.0,90.0,50.0,82,"Python, Git","AWS, Docker, Machine Learning, Agile development, Flask, REST API, MySQL",Estimated experience level matches 100% of requirements,"Consider developing skills in: AWS, Docker, Machine Learning, Agile development, Flask; Include more industry-specific keywords and terminology"
2,<PERSON><PERSON>,Profile_4.pdf,52.2,0.0,100.0,95.0,0.0,82,,"A<PERSON>, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: AWS, Docker, Machine Learning, Agile development; Include more industry-specific keywords and terminology"
3,<PERSON><PERSON><PERSON>,Profile_2.pdf,51.2,0.0,100.0,90.0,0.0,82,,"<PERSON><PERSON>, <PERSON><PERSON>, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: <PERSON><PERSON>, <PERSON>er, Machine Learning, Agile development; Include more industry-specific keywords and terminology"
4,<PERSON><PERSON> Nanaware,Profile_12.pdf,48.5,15.0,100.0,95.0,0.0,0,,"Python, Django, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, REST API, Git; Include more industry-specific keywords and terminology"
5,Arindam Mukherjee,Profile_19.pdf,45.2,7.5,100.0,90.0,0.0,0,,"Python, Django, PostgreSQL, REST API, Git",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, REST API, Git; Include more industry-specific keywords and terminology"
6,Riddhi Dutta,Profile_8.pdf,45.2,7.5,100.0,90.0,0.0,0,,"Python, Django, PostgreSQL, REST API, Git, AWS, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, REST API, Git; Include more industry-specific keywords and terminology"
7,Kaviya E.,Profile_21.pdf,44.0,0.0,100.0,95.0,0.0,0,,"Python, Django, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, REST API, Git; Include more industry-specific keywords and terminology"
8,Preksha Jain,Profile_1.pdf,43.0,0.0,100.0,90.0,0.0,0,,"Python, Django, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, REST API, Git; Include more industry-specific keywords and terminology"
9,Nisarg Gogate,Profile_10.pdf,43.0,0.0,100.0,90.0,0.0,0,,"Python, Django, Flask, PostgreSQL, REST API, Git",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
10,Raghavendra (Raghu) Uppalli,Profile_13.pdf,43.0,0.0,100.0,90.0,0.0,0,,"Python, Django, PostgreSQL, REST API, Git",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, REST API, Git; Include more industry-specific keywords and terminology"
11,Dharinee Gupta,Profile_17.pdf,43.0,0.0,100.0,90.0,0.0,0,,"Python, Django, PostgreSQL, REST API, Git",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, REST API, Git; Include more industry-specific keywords and terminology"
12,Vijay Komarapu,Profile_14.pdf,43.0,0.0,100.0,90.0,0.0,0,,"Python, Django, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, REST API, Git; Include more industry-specific keywords and terminology"
13,Kartick Kumar Nayagam,Profile_18.pdf,43.0,0.0,100.0,90.0,0.0,0,,"Python, Django, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, REST API, Git; Include more industry-specific keywords and terminology"
14,Sanika Jain,Profile_5.pdf,43.0,0.0,100.0,90.0,0.0,0,,"Python, Django, PostgreSQL, REST API, Git",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, REST API, Git; Include more industry-specific keywords and terminology"
15,Priyansh Agarwal,Profile_7.pdf,43.0,0.0,100.0,90.0,0.0,0,,"Python, Django, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, REST API, Git; Include more industry-specific keywords and terminology"
16,Arsh Goyal,Profile_6.pdf,43.0,0.0,100.0,90.0,0.0,0,,"Python, Django, Flask, PostgreSQL, REST API, Git",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
17,Suyash Gaidhani,Profile_15.pdf,25.0,0.0,100.0,0.0,0.0,0,,"Python, Django, PostgreSQL, REST API, Git",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, PostgreSQL, REST API, Git; Consider pursuing Bachelor's degree if not already obtained; Include more industry-specific keywords and terminology"
18,,Profile_9.pdf,13.5,0.0,0.0,30.0,50.0,0,,"AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 0% of requirements,"Consider developing skills in: AWS, Docker, Machine Learning, Agile development; Gain more experience to meet the 3+ years requirement; Consider pursuing Bachelor's degree if not already obtained; Include more industry-specific keywords and terminology"
19,,Profile_16.pdf,6.0,0.0,0.0,30.0,0.0,0,,"AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 0% of requirements,"Consider developing skills in: AWS, Docker, Machine Learning, Agile development; Gain more experience to meet the 3+ years requirement; Consider pursuing Bachelor's degree if not already obtained; Include more industry-specific keywords and terminology"
20,,Profile_3.pdf,6.0,0.0,0.0,30.0,0.0,0,,"AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 0% of requirements,"Consider developing skills in: AWS, Docker, Machine Learning, Agile development; Gain more experience to meet the 3+ years requirement; Consider pursuing Bachelor's degree if not already obtained; Include more industry-specific keywords and terminology"
21,,Profile_20.pdf,6.0,0.0,0.0,30.0,0.0,0,,"AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 0% of requirements,"Consider developing skills in: AWS, Docker, Machine Learning, Agile development; Gain more experience to meet the 3+ years requirement; Consider pursuing Bachelor's degree if not already obtained; Include more industry-specific keywords and terminology"
