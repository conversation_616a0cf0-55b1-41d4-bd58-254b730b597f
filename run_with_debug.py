#!/usr/bin/env python3
"""
Run Resume Parser with Debug Output Enabled
This script automatically enables all debug options and runs the parser
"""

import sys
from pathlib import Path

def main():
    """Main function to run with debug enabled"""
    print("🔍 RESUME PARSER - DEBUG MODE")
    print("=" * 50)
    
    # Import and enable all debug options
    from config import DEBUG_CONFIG
    
    # Enable all debug options
    DEBUG_CONFIG["enabled"] = True
    DEBUG_CONFIG["show_ollama_responses"] = True
    DEBUG_CONFIG["show_json_parsing"] = True
    DEBUG_CONFIG["show_scoring_details"] = True
    DEBUG_CONFIG["show_resume_analysis"] = True
    
    print("✅ Debug mode enabled - you will see:")
    print("   🤖 Raw Ollama responses")
    print("   📄 JSON parsing details")
    print("   🎯 Scoring process")
    print("   📋 Resume analysis steps")
    print("=" * 50)
    
    # Import main application
    from main import main as main_app
    
    # Run the main application
    main_app()

if __name__ == "__main__":
    main()
