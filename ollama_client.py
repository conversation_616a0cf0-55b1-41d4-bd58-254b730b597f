"""
Ollama LLM Integration Module
Handles communication with Ollama for resume analysis and scoring
"""

import json
import logging
import time
from typing import Dict, Any, Optional, List
import requests
from config import OLLAMA_CONFIG, PROMPT_TEMPLATES, DEBUG_CONFIG

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OllamaClient:
    """Client for interacting with Ollama LLM"""
    
    def __init__(self, base_url: str = None, model: str = None):
        self.base_url = base_url or OLLAMA_CONFIG["base_url"]
        self.model = model or OLLAMA_CONFIG["model"]
        self.timeout = OLLAMA_CONFIG["timeout"]
        self.temperature = OLLAMA_CONFIG["temperature"]
        
        # Test connection on initialization
        self.is_connected = self._test_connection()
    
    def _test_connection(self) -> bool:
        """Test connection to Ollama server"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            if response.status_code == 200:
                logger.info("✅ Successfully connected to Ollama server")
                
                # Check if our model is available
                models = response.json().get("models", [])
                model_names = [model["name"] for model in models]
                
                if any(self.model in name for name in model_names):
                    logger.info(f"✅ Model '{self.model}' is available")
                    return True
                else:
                    logger.warning(f"⚠️ Model '{self.model}' not found. Available models: {model_names}")
                    logger.info(f"💡 To install the model, run: ollama pull {self.model}")
                    return False
            else:
                logger.error(f"❌ Failed to connect to Ollama server: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Cannot connect to Ollama server at {self.base_url}: {e}")
            logger.info("💡 Make sure Ollama is running. Start it with: ollama serve")
            return False
    
    def _make_request(self, prompt: str, max_retries: int = 3) -> Optional[str]:
        """Make a request to Ollama with retry logic"""
        if not self.is_connected:
            logger.error("❌ Not connected to Ollama server")
            return None
        
        payload = {
            "model": self.model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": self.temperature,
                "top_p": 0.9,
                "top_k": 40
            }
        }
        
        for attempt in range(max_retries):
            try:
                logger.info(f"🔄 Making request to Ollama (attempt {attempt + 1}/{max_retries})")
                start_time = time.time()
                
                response = requests.post(
                    f"{self.base_url}/api/generate",
                    json=payload,
                    timeout=self.timeout
                )
                
                end_time = time.time()
                logger.info(f"⏱️ Request completed in {end_time - start_time:.2f} seconds")
                
                if response.status_code == 200:
                    result = response.json()
                    raw_response = result.get("response", "")

                    # Debug: Print raw Ollama response
                    if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_ollama_responses", False):
                        print(f"\n🤖 DEBUG: Raw Ollama Response (attempt {attempt + 1}):")
                        print(f"📝 Response length: {len(raw_response)} characters")
                        print(f"📄 Raw response preview: {raw_response[:300]}...")
                        if len(raw_response) > 300:
                            print(f"📄 Response end: ...{raw_response[-100:]}")
                        print("=" * 80)

                    return raw_response
                else:
                    logger.error(f"❌ Request failed with status {response.status_code}: {response.text}")
                    
            except requests.exceptions.Timeout:
                logger.warning(f"⏰ Request timed out (attempt {attempt + 1})")
            except requests.exceptions.RequestException as e:
                logger.error(f"❌ Request error: {e}")
            
            if attempt < max_retries - 1:
                wait_time = 2 ** attempt  # Exponential backoff
                logger.info(f"⏳ Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)
        
        logger.error("❌ All retry attempts failed")
        return None
    
    def _parse_json_response(self, response: str) -> Optional[Dict[str, Any]]:
        """Parse JSON response from Ollama, handling common issues"""
        if not response:
            if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_json_parsing", False):
                print("🚨 DEBUG: Empty response received")
            return None

        # Clean the response
        response = response.strip()
        if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_json_parsing", False):
            print(f"🔍 DEBUG: Parsing JSON from response (length: {len(response)})")

        # Try to find JSON in the response
        json_start = response.find('{')
        json_end = response.rfind('}') + 1

        if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_json_parsing", False):
            print(f"🔍 DEBUG: JSON boundaries - start: {json_start}, end: {json_end}")

        if json_start != -1 and json_end > json_start:
            json_str = response[json_start:json_end]
            if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_json_parsing", False):
                print(f"📄 DEBUG: Extracted JSON string: {json_str[:200]}...")

            try:
                parsed_json = json.loads(json_str)
                if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_json_parsing", False):
                    print(f"✅ DEBUG: Successfully parsed JSON with keys: {list(parsed_json.keys())}")
                return parsed_json
            except json.JSONDecodeError as e:
                if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_json_parsing", False):
                    print(f"⚠️ DEBUG: JSON parsing error: {e}")
                    print(f"📄 DEBUG: Problematic JSON: {json_str}")
                logger.warning(f"⚠️ JSON parsing error: {e}")
                logger.debug(f"Raw response: {response}")

                # Try to fix common JSON issues
                try:
                    # Fix trailing commas
                    fixed_json = json_str.replace(',}', '}').replace(',]', ']')
                    if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_json_parsing", False):
                        print(f"🔧 DEBUG: Attempting to fix JSON: {fixed_json[:200]}...")
                    parsed_fixed = json.loads(fixed_json)
                    if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_json_parsing", False):
                        print(f"✅ DEBUG: Successfully parsed fixed JSON")
                    return parsed_fixed
                except json.JSONDecodeError as e2:
                    if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_json_parsing", False):
                        print(f"❌ DEBUG: Could not fix JSON: {e2}")
                    logger.error("❌ Could not parse JSON response")
                    return None

        if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_json_parsing", False):
            print("❌ DEBUG: No valid JSON boundaries found in response")
        logger.error("❌ No valid JSON found in response")
        return None
    
    def analyze_resume(self, resume_text: str) -> Optional[Dict[str, Any]]:
        """Analyze resume and extract structured information"""
        if not resume_text.strip():
            logger.error("❌ Empty resume text provided")
            return None
        
        prompt = PROMPT_TEMPLATES["resume_analysis"].format(resume_text=resume_text)
        
        logger.info("🔍 Analyzing resume with Ollama...")
        response = self._make_request(prompt)
        
        if response:
            parsed_response = self._parse_json_response(response)
            if parsed_response:
                logger.info("✅ Resume analysis completed successfully")
                return parsed_response
            else:
                logger.error("❌ Failed to parse resume analysis response")
                # Return a basic structure if parsing fails
                return {
                    "contact_info": {"name": "Unknown", "email": "", "phone": "", "location": ""},
                    "skills": [],
                    "experience": [],
                    "education": [],
                    "certifications": [],
                    "summary": "Analysis failed - could not parse response"
                }
        
        return None
    
    def analyze_job_description(self, job_description: str) -> Optional[Dict[str, Any]]:
        """Analyze job description and extract requirements"""
        if not job_description.strip():
            logger.error("❌ Empty job description provided")
            return None
        
        prompt = PROMPT_TEMPLATES["job_analysis"].format(job_description=job_description)
        
        logger.info("🔍 Analyzing job description with Ollama...")
        response = self._make_request(prompt)
        
        if response:
            parsed_response = self._parse_json_response(response)
            if parsed_response:
                logger.info("✅ Job description analysis completed successfully")
                return parsed_response
            else:
                logger.error("❌ Failed to parse job description analysis response")
                # Return a basic structure if parsing fails
                return {
                    "required_skills": [],
                    "preferred_skills": [],
                    "experience_requirements": {"min_years": 0, "preferred_years": 0, "required_experience": []},
                    "education_requirements": {"required_degree": "", "preferred_degree": "", "fields": []},
                    "keywords": [],
                    "job_level": "Unknown",
                    "industry": "Unknown"
                }
        
        return None
    
    def score_resume_match(self, resume_data: Dict[str, Any], job_requirements: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Score how well a resume matches job requirements"""
        
        # Prepare resume summary for scoring
        resume_summary = resume_data.get("summary", "")
        resume_skills = ", ".join(resume_data.get("skills", []))
        
        # Format experience for prompt
        experience_list = []
        for exp in resume_data.get("experience", []):
            exp_str = f"{exp.get('title', '')} at {exp.get('company', '')} ({exp.get('duration', '')})"
            experience_list.append(exp_str)
        resume_experience = "; ".join(experience_list)
        
        # Create job description summary
        job_description = f"""
Required Skills: {', '.join(job_requirements.get('required_skills', []))}
Preferred Skills: {', '.join(job_requirements.get('preferred_skills', []))}
Experience Requirements: {job_requirements.get('experience_requirements', {})}
Education Requirements: {job_requirements.get('education_requirements', {})}
Keywords: {', '.join(job_requirements.get('keywords', []))}
Job Level: {job_requirements.get('job_level', 'Unknown')}
"""
        
        prompt = PROMPT_TEMPLATES["job_matching"].format(
            job_description=job_description,
            resume_summary=resume_summary,
            resume_skills=resume_skills,
            resume_experience=resume_experience
        )
        
        logger.info("🎯 Scoring resume match with Ollama...")
        response = self._make_request(prompt)
        
        if response:
            parsed_response = self._parse_json_response(response)
            if parsed_response:
                logger.info("✅ Resume scoring completed successfully")
                return parsed_response
            else:
                logger.error("❌ Failed to parse resume scoring response")
                # Return default scores if parsing fails
                return {
                    "scores": {
                        "skills_match": 50,
                        "experience_match": 50,
                        "education_match": 50,
                        "keywords_match": 50,
                        "overall_relevance": 50
                    },
                    "matching_skills": [],
                    "missing_skills": [],
                    "experience_relevance": "Could not analyze",
                    "recommendations": "Analysis failed",
                    "overall_score": 50
                }
        
        return None
    
    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            if response.status_code == 200:
                models = response.json().get("models", [])
                return [model["name"] for model in models]
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Error getting models: {e}")
        
        return []
    
    def pull_model(self, model_name: str) -> bool:
        """Pull a model from Ollama registry"""
        try:
            logger.info(f"📥 Pulling model: {model_name}")
            response = requests.post(
                f"{self.base_url}/api/pull",
                json={"name": model_name},
                timeout=300  # 5 minutes timeout for model download
            )
            
            if response.status_code == 200:
                logger.info(f"✅ Successfully pulled model: {model_name}")
                return True
            else:
                logger.error(f"❌ Failed to pull model: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Error pulling model: {e}")
            return False

# Utility functions
def test_ollama_connection():
    """Test Ollama connection and model availability"""
    client = OllamaClient()
    
    if not client.is_connected:
        print("❌ Ollama connection failed")
        print("💡 Make sure Ollama is running and the model is installed")
        return False
    
    print("✅ Ollama connection successful")
    
    # Test with a simple prompt
    test_response = client._make_request("Hello, respond with just 'OK' if you can understand this.")
    if test_response:
        print("✅ Ollama is responding correctly")
        return True
    else:
        print("❌ Ollama is not responding")
        return False

if __name__ == "__main__":
    # Test the Ollama client
    test_ollama_connection()
