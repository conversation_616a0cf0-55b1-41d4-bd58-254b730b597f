rank,candidate_name,file_name,overall_score,skills_match,experience_match,education_match,keywords_match,overall_relevance,matching_skills,missing_skills,experience_relevance,recommendations
1,<PERSON><PERSON><PERSON><PERSON>,Profile_11.pdf,68.0,30.83333333333333,100.0,90.0,50.0,82,"git, python, machine learning","Django, Flask, PostgreSQL, REST API, AWS, Docker, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Django, Flask, PostgreSQL, REST API, AWS; Include more industry-specific keywords and terminology"
2,Vidhyasa<PERSON> P,Profile_9.pdf,62.2,11.666666666666664,100.0,90.0,50.0,82,django,"Python, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Flask, PostgreSQL, REST API, Git; Include more industry-specific keywords and terminology"
3,<PERSON><PERSON>,Profile_12.pdf,56.7,15.0,100.0,95.0,0.0,82,"docker, machine learning","Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
4,Vijay Komarapu,Profile_14.pdf,55.7,15.0,100.0,90.0,0.0,82,"docker, machine learning","Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
5,Riddhi Dutta,Profile_8.pdf,53.5,7.5,100.0,90.0,0.0,82,aws,"Python, Django, Flask, PostgreSQL, REST API, Git, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
6,Kaviya E.,Profile_21.pdf,52.2,0.0,100.0,95.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
7,Nivetha S,Profile_20.pdf,52.2,0.0,100.0,95.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
8,Nisarg Gogate,Profile_10.pdf,51.2,0.0,100.0,90.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
9,Preksha Jain,Profile_1.pdf,51.2,0.0,100.0,90.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
10,Raghavendra (Raghu) Uppalli,Profile_13.pdf,51.2,0.0,100.0,90.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
11,Dharinee Gupta,Profile_17.pdf,51.2,0.0,100.0,90.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
12,Kartick Kumar Nayagam,Profile_18.pdf,51.2,0.0,100.0,90.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
13,Arindam Mukherjee,Profile_19.pdf,51.2,0.0,100.0,90.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
14,Vasu Beri,Profile_2.pdf,51.2,0.0,100.0,90.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
15,Sanika Jain,Profile_5.pdf,51.2,0.0,100.0,90.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
16,Priyansh Agarwal,Profile_7.pdf,51.2,0.0,100.0,90.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
17,Arsh Goyal,Profile_6.pdf,51.2,0.0,100.0,90.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Include more industry-specific keywords and terminology"
18,Suyash Gaidhani,Profile_15.pdf,33.2,0.0,100.0,0.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Consider pursuing Bachelor's degree if not already obtained; Include more industry-specific keywords and terminology"
19,Bill Allocca,Profile_16.pdf,33.2,0.0,100.0,0.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Consider pursuing Bachelor's degree if not already obtained; Include more industry-specific keywords and terminology"
20,Fahad Ibraheem,Profile_3.pdf,33.2,0.0,100.0,0.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 100% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Consider pursuing Bachelor's degree if not already obtained; Include more industry-specific keywords and terminology"
21,Unknown,Profile_4.pdf,14.2,0.0,0.0,30.0,0.0,82,,"Python, Django, Flask, PostgreSQL, REST API, Git, AWS, Docker, Machine Learning, Agile development",Estimated experience level matches 0% of requirements,"Consider developing skills in: Python, Django, Flask, PostgreSQL, REST API; Gain more experience to meet the 3+ years requirement; Consider pursuing Bachelor's degree if not already obtained; Include more industry-specific keywords and terminology"
