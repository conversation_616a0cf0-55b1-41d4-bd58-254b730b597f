#!/usr/bin/env python3
"""
Run Resume Parser with Clean Output (No Debug)
This script disables debug output for clean, production-like results
"""

import sys
from pathlib import Path

def main():
    """Main function to run with clean output"""
    print("🚀 RESUME PARSER - CLEAN MODE")
    print("=" * 40)
    
    # Import and disable debug options
    from config import DEBUG_CONFIG
    
    # Disable debug options for clean output
    DEBUG_CONFIG["enabled"] = False
    DEBUG_CONFIG["show_ollama_responses"] = False
    DEBUG_CONFIG["show_json_parsing"] = False
    DEBUG_CONFIG["show_scoring_details"] = False
    DEBUG_CONFIG["show_resume_analysis"] = False
    
    print("✅ Clean mode enabled - minimal output")
    print("=" * 40)
    
    # Import main application
    from main import main as main_app
    
    # Run the main application
    main_app()

if __name__ == "__main__":
    main()
