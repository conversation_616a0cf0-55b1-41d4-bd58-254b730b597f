#!/usr/bin/env python3
"""
Test Validation System
Verify that the validation prevents AI hallucination
"""

import sys
from pathlib import Path

def test_skill_validation():
    """Test skill validation against hallucination"""
    print("🧪 Testing Skill Validation System")
    print("=" * 50)
    
    from validation import ResponseValidator
    
    validator = ResponseValidator()
    
    # Test case 1: Resume with HR skills, AI claims Python skills
    hr_resume_text = """
    Kaviya E.
    HR - Talent Acquisition
    Email: <EMAIL>
    
    SKILLS:
    - Recruitment
    - Talent Acquisition
    - HR Management
    - Interview Coordination
    - Employee Relations
    
    EXPERIENCE:
    HR Specialist | ABC Corp | 2020-2023
    - Managed recruitment process
    - Conducted interviews
    - Coordinated with hiring managers
    """
    
    # Simulated AI response with hallucinated skills
    fake_ai_response = {
        "contact_info": {
            "name": "Kaviya E.",
            "email": "<EMAIL>"
        },
        "skills": ["Python", "Django", "Recruitment", "Talent Acquisition", "JavaScript"],  # Python/Django are hallucinated
        "experience": [
            {
                "title": "HR Specialist",
                "company": "ABC Corp",
                "duration": "2020-2023"
            }
        ],
        "education": [],
        "certifications": [],
        "summary": "HR professional with recruitment experience"
    }
    
    print("📄 Test Resume: HR professional")
    print(f"🤖 AI claimed skills: {fake_ai_response['skills']}")
    
    # Validate the response
    validated_response = validator.validate_resume_analysis(fake_ai_response, hr_resume_text)
    
    print(f"✅ Validated skills: {validated_response['skills']}")
    print(f"🔍 Validation result: {'PASSED' if 'Python' not in validated_response['skills'] else 'FAILED'}")
    
    return 'Python' not in validated_response['skills']

def test_job_matching_validation():
    """Test job matching validation"""
    print("\n🧪 Testing Job Matching Validation")
    print("=" * 50)
    
    from validation import ResponseValidator
    
    validator = ResponseValidator()
    
    # Actual resume skills (HR professional)
    actual_skills = ["Recruitment", "Talent Acquisition", "HR Management", "Interview Coordination"]
    
    # Simulated AI matching response with hallucinated matching skills
    fake_matching_response = {
        "scores": {
            "skills_match": 85,
            "experience_match": 75,
            "education_match": 90,
            "keywords_match": 80,
            "overall_relevance": 82
        },
        "matching_skills": ["Python", "Django", "Recruitment"],  # Python/Django are hallucinated
        "missing_skills": ["Flask", "PostgreSQL"],
        "experience_relevance": "Good experience",
        "recommendations": "Learn more programming",
        "overall_score": 82
    }
    
    print(f"🛠️ Actual resume skills: {actual_skills}")
    print(f"🤖 AI claimed matching skills: {fake_matching_response['matching_skills']}")
    
    # Validate the matching response
    validated_matching = validator.validate_job_matching(fake_matching_response, actual_skills)
    
    print(f"✅ Validated matching skills: {validated_matching['matching_skills']}")
    print(f"🔍 Validation result: {'PASSED' if 'Python' not in validated_matching['matching_skills'] else 'FAILED'}")
    
    return 'Python' not in validated_matching['matching_skills']

def test_with_real_resume():
    """Test with a real resume from the system"""
    print("\n🧪 Testing with Real Resume Processing")
    print("=" * 50)
    
    from bulk_processor import BulkProcessor
    from config import DEFAULT_JOB_DESCRIPTION
    
    processor = BulkProcessor()
    
    # Find resume files
    resume_files = processor.find_resume_files()
    if not resume_files:
        print("❌ No resume files found for testing")
        return False
    
    print(f"📄 Testing with: {resume_files[0].name}")
    
    # Process one resume with validation
    try:
        analysis_result, resume_text = processor.parse_single_resume(resume_files[0])
        
        if analysis_result:
            print(f"✅ Resume processed successfully")
            print(f"🛠️ Extracted skills: {analysis_result.get('skills', [])}")
            print(f"📝 Contact: {analysis_result.get('contact_info', {}).get('name', 'Unknown')}")
            
            # Check if skills make sense for the resume type
            skills = analysis_result.get('skills', [])
            tech_skills = ['python', 'django', 'javascript', 'java', 'react', 'node.js']
            hr_skills = ['recruitment', 'talent', 'hr', 'interview', 'hiring']
            
            has_tech = any(skill.lower() in tech_skills for skill in skills)
            has_hr = any(any(hr_word in skill.lower() for hr_word in hr_skills) for skill in skills)
            
            print(f"🔍 Analysis: Tech skills detected: {has_tech}, HR skills detected: {has_hr}")
            
            return True
        else:
            print("❌ Failed to process resume")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

def main():
    """Main test function"""
    print("🔍 VALIDATION SYSTEM TESTING")
    print("=" * 60)
    
    print("This test verifies that the validation system prevents AI hallucination")
    print("by ensuring extracted skills actually exist in the resume text.\n")
    
    # Run tests
    test1_passed = test_skill_validation()
    test2_passed = test_job_matching_validation()
    test3_passed = test_with_real_resume()
    
    # Summary
    print("\n📊 TEST RESULTS SUMMARY")
    print("=" * 30)
    print(f"Skill Validation Test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Job Matching Validation Test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    print(f"Real Resume Test: {'✅ PASSED' if test3_passed else '❌ FAILED'}")
    
    all_passed = test1_passed and test2_passed and test3_passed
    print(f"\nOverall Result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 Validation system is working correctly!")
        print("The system will now prevent AI hallucination and only extract")
        print("skills and information that actually exist in the resume text.")
    else:
        print("\n⚠️ Some validation tests failed. Check the implementation.")

if __name__ == "__main__":
    main()
