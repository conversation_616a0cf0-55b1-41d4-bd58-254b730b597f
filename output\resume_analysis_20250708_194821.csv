rank,candidate_name,file_name,overall_score,skills_match,experience_match,education_match,keywords_match,overall_relevance,matching_skills,missing_skills,experience_relevance,recommendations
1,AKURATHI SASIDHAR,sasidhar.pdf,39.6,7.0,0.0,90,75.0,82,"Python, MySQL","Git, agile, Flask, azure, aws, docker, rest api, PostgreSQL, Problem-solving skills, machine learning, Django, Docker and containerization, Agile development experience, Cloud platforms (AWS, Azure), Machine Learning experience, 5+ years of experience, Master's degree",Estimated experience level matches 0% of requirements,"Consider developing skills in: Git, agile, Flask, azure, aws; Gain more experience to meet the 3+ years requirement"
