"""
Bulk Processing Engine
Handles batch processing of multiple resumes with progress tracking
"""

import logging
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from dataclasses import dataclass
from tqdm import tqdm
import json

from resume_parser import ResumeParser
from ollama_client import <PERSON>llamaClient
from job_matcher import JobMatcher, JobRequirements, ResumeProfile, MatchingResult
from config import PERFORMANCE_CONFIG, RESUMES_DIR, OUTPUT_DIR, DEBUG_CONFIG

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ProcessingStats:
    """Statistics for bulk processing"""
    total_files: int = 0
    successful_parses: int = 0
    failed_parses: int = 0
    successful_analyses: int = 0
    failed_analyses: int = 0
    processing_time: float = 0.0
    average_score: float = 0.0
    top_score: float = 0.0

class BulkProcessor:
    """Main class for bulk resume processing"""
    
    def __init__(self, resume_directory: Path = None, output_directory: Path = None):
        self.resume_directory = resume_directory or RESUMES_DIR
        self.output_directory = output_directory or OUTPUT_DIR
        
        # Initialize components
        self.resume_parser = ResumeParser()
        self.ollama_client = OllamaClient()
        self.job_matcher = JobMatcher(self.ollama_client)
        
        # Performance settings
        self.batch_size = PERFORMANCE_CONFIG["batch_size"]
        self.max_workers = PERFORMANCE_CONFIG["max_workers"]
        
        # Ensure directories exist
        self.resume_directory.mkdir(exist_ok=True)
        self.output_directory.mkdir(exist_ok=True)
        
        logger.info(f"🚀 Bulk processor initialized")
        logger.info(f"📁 Resume directory: {self.resume_directory}")
        logger.info(f"📁 Output directory: {self.output_directory}")
    
    def find_resume_files(self) -> List[Path]:
        """Find all supported resume files in the directory"""
        supported_files = []
        
        for file_path in self.resume_directory.iterdir():
            if file_path.is_file() and self.resume_parser.is_supported_format(file_path):
                supported_files.append(file_path)
        
        logger.info(f"📄 Found {len(supported_files)} resume files")
        return supported_files
    
    def parse_single_resume(self, file_path: Path) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """Parse a single resume file"""
        try:
            # Parse the file
            parse_result = self.resume_parser.parse_resume(file_path)

            if not parse_result["success"]:
                logger.error(f"❌ Failed to parse {file_path.name}: {parse_result['error']}")
                return None, None

            # Analyze with Ollama
            resume_text = parse_result["text"]
            if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_resume_analysis", False):
                print(f"\n🔍 DEBUG: Analyzing resume {file_path.name}")
                print(f"📄 Resume text preview (first 200 chars): {resume_text[:200]}...")

            analysis_result = self.ollama_client.analyze_resume(resume_text)

            # Debug: Print Ollama response
            if analysis_result:
                if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_resume_analysis", False):
                    print(f"🤖 DEBUG: Ollama analysis result for {file_path.name}:")
                    print(f"   📝 Contact Info: {analysis_result.get('contact_info', {})}")
                    print(f"   🛠️ Skills: {analysis_result.get('skills', [])}")
                    print(f"   💼 Experience: {len(analysis_result.get('experience', []))} entries")
                    print(f"   🎓 Education: {len(analysis_result.get('education', []))} entries")
                    print(f"   📋 Summary: {analysis_result.get('summary', 'N/A')[:100]}...")
            else:
                if DEBUG_CONFIG.get("enabled", False) and DEBUG_CONFIG.get("show_resume_analysis", False):
                    print(f"❌ DEBUG: No analysis result for {file_path.name}")
                logger.error(f"❌ Failed to analyze {file_path.name}")
                return None, resume_text

            return analysis_result, resume_text

        except Exception as e:
            logger.error(f"❌ Error processing {file_path.name}: {e}")
            print(f"🚨 DEBUG: Exception in parse_single_resume: {e}")
            return None, None
    
    def process_resumes_batch(self, file_paths: List[Path]) -> List[Tuple[Optional[ResumeProfile], str]]:
        """Process a batch of resumes in parallel"""
        results = []
        
        # Use ThreadPoolExecutor for I/O bound operations
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_file = {
                executor.submit(self.parse_single_resume, file_path): file_path 
                for file_path in file_paths
            }
            
            # Collect results with progress bar
            with tqdm(total=len(file_paths), desc="Parsing resumes", unit="file") as pbar:
                for future in as_completed(future_to_file):
                    file_path = future_to_file[future]
                    
                    try:
                        analysis_result, resume_text = future.result()
                        
                        if analysis_result and resume_text:
                            # Create resume profile
                            profile = self.job_matcher.create_resume_profile(analysis_result, resume_text)
                            results.append((profile, file_path.name))
                        else:
                            results.append((None, file_path.name))
                            
                    except Exception as e:
                        logger.error(f"❌ Exception processing {file_path.name}: {e}")
                        results.append((None, file_path.name))
                    
                    pbar.update(1)
        
        return results
    
    def process_all_resumes(self, job_description: str) -> Tuple[List[MatchingResult], ProcessingStats]:
        """Process all resumes against a job description"""
        start_time = time.time()
        
        logger.info("🔄 Starting bulk resume processing...")
        
        # Find all resume files
        resume_files = self.find_resume_files()
        
        if not resume_files:
            logger.warning("⚠️ No resume files found")
            return [], ProcessingStats()
        
        # Analyze job description
        logger.info("🔍 Analyzing job description...")
        job_requirements = self.job_matcher.analyze_job_description(job_description)
        
        if not job_requirements:
            logger.error("❌ Failed to analyze job description")
            return [], ProcessingStats()
        
        logger.info(f"✅ Job analysis complete. Required skills: {len(job_requirements.required_skills)}")
        
        # Process resumes in batches
        all_results = []
        stats = ProcessingStats(total_files=len(resume_files))
        
        # Split files into batches
        batches = [resume_files[i:i + self.batch_size] 
                  for i in range(0, len(resume_files), self.batch_size)]
        
        logger.info(f"📦 Processing {len(batches)} batches of up to {self.batch_size} files each")
        
        for batch_num, batch_files in enumerate(batches, 1):
            logger.info(f"🔄 Processing batch {batch_num}/{len(batches)}")
            
            # Parse resumes in this batch
            batch_results = self.process_resumes_batch(batch_files)
            
            # Match each resume to job requirements
            for profile, file_name in batch_results:
                if profile:
                    try:
                        match_result = self.job_matcher.match_resume_to_job(
                            profile, job_requirements, file_name
                        )
                        all_results.append(match_result)
                        stats.successful_analyses += 1
                        
                    except Exception as e:
                        logger.error(f"❌ Error matching {file_name}: {e}")
                        stats.failed_analyses += 1
                else:
                    stats.failed_parses += 1
            
            # Small delay between batches to prevent overwhelming the system
            if batch_num < len(batches):
                time.sleep(1)
        
        # Sort results by score
        all_results.sort(key=lambda x: x.overall_score, reverse=True)
        
        # Calculate final statistics
        end_time = time.time()
        stats.processing_time = end_time - start_time
        stats.successful_parses = stats.successful_analyses  # Successful analysis implies successful parse
        
        if all_results:
            stats.average_score = sum(r.overall_score for r in all_results) / len(all_results)
            stats.top_score = all_results[0].overall_score
        
        logger.info("✅ Bulk processing completed!")
        logger.info(f"📊 Processed {stats.total_files} files in {stats.processing_time:.2f} seconds")
        logger.info(f"📊 Success rate: {stats.successful_analyses}/{stats.total_files} ({stats.successful_analyses/stats.total_files*100:.1f}%)")
        
        return all_results, stats
    
    def save_results(self, results: List[MatchingResult], stats: ProcessingStats, job_description: str) -> Dict[str, Path]:
        """Save processing results to files"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        saved_files = {}
        
        # Prepare data for export
        results_data = []
        for result in results:
            results_data.append({
                "rank": len(results_data) + 1,
                "candidate_name": result.candidate_name,
                "file_name": result.file_name,
                "overall_score": result.overall_score,
                "skills_match": result.scores["skills_match"],
                "experience_match": result.scores["experience_match"],
                "education_match": result.scores["education_match"],
                "keywords_match": result.scores["keywords_match"],
                "overall_relevance": result.scores["overall_relevance"],
                "matching_skills": ", ".join(result.matching_skills),
                "missing_skills": ", ".join(result.missing_skills),
                "experience_relevance": result.experience_relevance,
                "recommendations": "; ".join(result.recommendations)
            })
        
        # Save as JSON
        json_file = self.output_directory / f"resume_analysis_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump({
                "job_description": job_description,
                "processing_stats": {
                    "total_files": stats.total_files,
                    "successful_analyses": stats.successful_analyses,
                    "failed_analyses": stats.failed_analyses,
                    "processing_time": stats.processing_time,
                    "average_score": stats.average_score,
                    "top_score": stats.top_score
                },
                "results": results_data
            }, f, indent=2)
        
        saved_files["json"] = json_file
        logger.info(f"💾 Results saved to: {json_file}")
        
        # Save as CSV
        try:
            import pandas as pd
            
            df = pd.DataFrame(results_data)
            csv_file = self.output_directory / f"resume_analysis_{timestamp}.csv"
            df.to_csv(csv_file, index=False)
            saved_files["csv"] = csv_file
            logger.info(f"💾 Results saved to: {csv_file}")
            
        except ImportError:
            logger.warning("⚠️ pandas not available, skipping CSV export")
        
        # Save summary report
        summary_file = self.output_directory / f"summary_report_{timestamp}.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("RESUME ANALYSIS SUMMARY REPORT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Processing Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total Files Processed: {stats.total_files}\n")
            f.write(f"Successful Analyses: {stats.successful_analyses}\n")
            f.write(f"Failed Analyses: {stats.failed_analyses}\n")
            f.write(f"Processing Time: {stats.processing_time:.2f} seconds\n")
            f.write(f"Average Score: {stats.average_score:.1f}\n")
            f.write(f"Top Score: {stats.top_score:.1f}\n\n")
            
            f.write("TOP 10 CANDIDATES:\n")
            f.write("-" * 30 + "\n")
            for i, result in enumerate(results[:10], 1):
                f.write(f"{i:2d}. {result.candidate_name} ({result.file_name})\n")
                f.write(f"    Score: {result.overall_score:.1f}\n")
                f.write(f"    Skills: {result.scores['skills_match']:.1f} | ")
                f.write(f"Experience: {result.scores['experience_match']:.1f} | ")
                f.write(f"Education: {result.scores['education_match']:.1f}\n\n")
        
        saved_files["summary"] = summary_file
        logger.info(f"💾 Summary saved to: {summary_file}")
        
        return saved_files
    
    def get_processing_status(self) -> Dict[str, Any]:
        """Get current processing status"""
        resume_files = self.find_resume_files()
        
        return {
            "resume_directory": str(self.resume_directory),
            "output_directory": str(self.output_directory),
            "resume_files_found": len(resume_files),
            "ollama_connected": self.ollama_client.is_connected,
            "batch_size": self.batch_size,
            "max_workers": self.max_workers
        }

# Utility functions
def quick_process(job_description: str, resume_directory: Path = None) -> List[MatchingResult]:
    """Quick processing function for simple use cases"""
    processor = BulkProcessor(resume_directory)
    results, stats = processor.process_all_resumes(job_description)
    
    if results:
        saved_files = processor.save_results(results, stats, job_description)
        logger.info(f"✅ Processing complete. Results saved to {len(saved_files)} files.")
    
    return results

if __name__ == "__main__":
    # Test the bulk processor
    from config import DEFAULT_JOB_DESCRIPTION
    
    processor = BulkProcessor()
    status = processor.get_processing_status()
    
    print("🔍 Bulk Processor Status:")
    for key, value in status.items():
        print(f"  {key}: {value}")
    
    if status["resume_files_found"] > 0 and status["ollama_connected"]:
        print("\n🚀 Ready to process resumes!")
        print("Run: python main.py to start processing")
    else:
        print("\n⚠️ Setup required:")
        if status["resume_files_found"] == 0:
            print("  - Add resume files to the 'resumes' directory")
        if not status["ollama_connected"]:
            print("  - Start Ollama server and install a model")
