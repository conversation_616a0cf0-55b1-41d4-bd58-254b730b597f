#!/usr/bin/env python3
"""
Test Missing Skills Validation
Verify that missing_skills only contains skills from the job description
"""

import sys
from pathlib import Path

def test_missing_skills_validation():
    """Test that missing_skills validation works correctly"""
    print("🧪 Testing Missing Skills Validation")
    print("=" * 50)
    
    from validation import ResponseValidator
    
    validator = ResponseValidator()
    
    # Define job skills (from default job description)
    job_required_skills = ["Python", "Django", "Flask", "PostgreSQL", "REST API", "Git"]
    job_preferred_skills = ["AWS", "Docker", "Machine Learning", "Agile development"]
    
    # Resume skills (HR professional)
    actual_resume_skills = ["Recruitment", "Talent Acquisition", "HR Management", "Interview Coordination"]
    
    # Simulated AI response with hallucinated missing skills
    fake_ai_response = {
        "scores": {
            "skills_match": 0,
            "experience_match": 75,
            "education_match": 90,
            "keywords_match": 20,
            "overall_relevance": 50
        },
        "matching_skills": [],  # Correct - no matching skills
        "missing_skills": [
            "Python", "Django", "Flask",  # These are correct (from job description)
            "Data Structures",  # This is WRONG (not in job description)
            "Object-oriented programming",  # This is WRONG (not in job description)
            "MySQL",  # This is WRONG (not in job description)
            "PostgreSQL", "REST API", "Git"  # These are correct (from job description)
        ],
        "experience_relevance": "No relevant experience",
        "recommendations": "Learn programming skills",
        "overall_score": 50
    }
    
    print("📋 Job Required Skills:", job_required_skills)
    print("📋 Job Preferred Skills:", job_preferred_skills)
    print("🛠️ Actual Resume Skills:", actual_resume_skills)
    print("🤖 AI Claimed Missing Skills:", fake_ai_response["missing_skills"])
    
    # Validate the response
    validated_response = validator.validate_job_matching(
        fake_ai_response, 
        actual_resume_skills,
        job_required_skills,
        job_preferred_skills
    )
    
    print("✅ Validated Missing Skills:", validated_response["missing_skills"])
    
    # Check if hallucinated skills were removed
    hallucinated_skills = ["Data Structures", "Object-oriented programming", "MySQL"]
    validation_passed = True
    
    for skill in hallucinated_skills:
        if skill in validated_response["missing_skills"]:
            print(f"❌ FAILED: Hallucinated skill '{skill}' was not removed")
            validation_passed = False
        else:
            print(f"✅ PASSED: Hallucinated skill '{skill}' was correctly removed")
    
    # Check if valid job skills are still present
    valid_missing_skills = ["Python", "Django", "Flask", "PostgreSQL", "REST API", "Git", "AWS", "Docker", "Machine Learning", "Agile development"]
    for skill in valid_missing_skills:
        if skill in fake_ai_response["missing_skills"] and skill not in validated_response["missing_skills"]:
            print(f"⚠️ WARNING: Valid job skill '{skill}' was incorrectly removed")
    
    return validation_passed

def test_with_real_job_description():
    """Test with the actual default job description"""
    print("\n🧪 Testing with Real Job Description")
    print("=" * 50)
    
    from config import DEFAULT_JOB_DESCRIPTION
    from job_matcher import JobMatcher
    
    matcher = JobMatcher()
    
    # Analyze the default job description
    job_requirements = matcher.analyze_job_description(DEFAULT_JOB_DESCRIPTION)
    
    if job_requirements:
        print("📋 Extracted Job Requirements:")
        print(f"   Required Skills: {job_requirements.required_skills}")
        print(f"   Preferred Skills: {job_requirements.preferred_skills}")
        
        # Test validation with these exact skills
        all_job_skills = job_requirements.required_skills + job_requirements.preferred_skills
        print(f"   All Job Skills: {all_job_skills}")
        
        # Simulate AI response with mixed valid/invalid missing skills
        test_response = {
            "scores": {"skills_match": 0, "experience_match": 50, "education_match": 70, "keywords_match": 30, "overall_relevance": 40},
            "matching_skills": [],
            "missing_skills": all_job_skills + ["Data Structures", "OOP", "Algorithms"],  # Add some invalid ones
            "experience_relevance": "Limited relevant experience",
            "recommendations": "Learn programming",
            "overall_score": 40
        }
        
        print(f"🤖 Test Missing Skills (with hallucinations): {test_response['missing_skills']}")
        
        # Validate
        from validation import ResponseValidator
        validator = ResponseValidator()
        validated = validator.validate_job_matching(
            test_response,
            ["HR Management", "Recruitment"],  # Sample resume skills
            job_requirements.required_skills,
            job_requirements.preferred_skills
        )
        
        print(f"✅ Validated Missing Skills: {validated['missing_skills']}")
        
        # Check if only job skills remain
        invalid_skills = ["Data Structures", "OOP", "Algorithms"]
        validation_passed = True
        
        for skill in invalid_skills:
            if skill in validated["missing_skills"]:
                print(f"❌ FAILED: Invalid skill '{skill}' was not removed")
                validation_passed = False
            else:
                print(f"✅ PASSED: Invalid skill '{skill}' was correctly removed")
        
        return validation_passed
    else:
        print("❌ Failed to analyze job description")
        return False

def test_end_to_end_validation():
    """Test end-to-end validation with actual processing"""
    print("\n🧪 Testing End-to-End Validation")
    print("=" * 50)
    
    from bulk_processor import BulkProcessor
    from config import DEFAULT_JOB_DESCRIPTION
    
    processor = BulkProcessor()
    
    # Find resume files
    resume_files = processor.find_resume_files()
    if not resume_files:
        print("❌ No resume files found for testing")
        return False
    
    print(f"📄 Testing with: {resume_files[0].name}")
    
    try:
        # Process one resume
        analysis_result, resume_text = processor.parse_single_resume(resume_files[0])
        
        if analysis_result:
            # Get job requirements
            job_requirements = processor.job_matcher.analyze_job_description(DEFAULT_JOB_DESCRIPTION)
            
            if job_requirements:
                print(f"🛠️ Resume Skills: {analysis_result.get('skills', [])}")
                print(f"📋 Job Required Skills: {job_requirements.required_skills}")
                print(f"📋 Job Preferred Skills: {job_requirements.preferred_skills}")
                
                # Create resume profile and match
                resume_profile = processor.job_matcher.create_resume_profile(analysis_result, resume_text)
                matching_result = processor.job_matcher.match_resume_to_job(resume_profile, job_requirements, resume_files[0].name)
                
                print(f"🎯 Matching Skills: {matching_result.matching_skills}")
                print(f"❌ Missing Skills: {matching_result.missing_skills}")
                
                # Validate that missing skills are only from job description
                all_job_skills = job_requirements.required_skills + job_requirements.preferred_skills
                invalid_missing = [skill for skill in matching_result.missing_skills if skill not in all_job_skills]
                
                if invalid_missing:
                    print(f"❌ FAILED: Found invalid missing skills: {invalid_missing}")
                    return False
                else:
                    print("✅ PASSED: All missing skills are from job description")
                    return True
            else:
                print("❌ Failed to analyze job description")
                return False
        else:
            print("❌ Failed to process resume")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

def main():
    """Main test function"""
    print("🔍 MISSING SKILLS VALIDATION TESTING")
    print("=" * 60)
    
    print("This test verifies that missing_skills only contains skills")
    print("from the job description and no hallucinated skills.\n")
    
    # Run tests
    test1_passed = test_missing_skills_validation()
    test2_passed = test_with_real_job_description()
    test3_passed = test_end_to_end_validation()
    
    # Summary
    print("\n📊 TEST RESULTS SUMMARY")
    print("=" * 30)
    print(f"Missing Skills Validation Test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Real Job Description Test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    print(f"End-to-End Validation Test: {'✅ PASSED' if test3_passed else '❌ FAILED'}")
    
    all_passed = test1_passed and test2_passed and test3_passed
    print(f"\nOverall Result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 Missing skills validation is working correctly!")
        print("The system will now ensure that:")
        print("✅ missing_skills only contains skills from the job description")
        print("✅ No hallucinated skills like 'Data Structures', 'OOP' are added")
        print("✅ matching_skills only contains skills present in the resume")
    else:
        print("\n⚠️ Some validation tests failed. Check the implementation.")

if __name__ == "__main__":
    main()
