{"job_description": "\nSoftware Engineer - Python Developer\n\nWe are looking for a skilled Python Developer to join our team.\n\nRequired Skills:\n- Python\n- Django\n- Flask\n- PostgreSQL\n- MySQL\n- REST API\n- Git\n\nPreferred Skills:\n- AWS\n- Azure\n- Docker\n- Machine Learning\n- Agile\n\nRequirements:\n- Bachelor's degree in Computer Science or related field\n- 3+ years of experience in Python development\n- Experience with web frameworks\n- Knowledge of databases\n- Problem-solving skills\n\nPreferred:\n- Master's degree\n- 5+ years of experience\n- Experience with cloud platforms\n- Docker and containerization experience\n- Machine Learning experience\n- Agile development experience\n", "processing_stats": {"total_files": 1, "successful_analyses": 1, "failed_analyses": 0, "processing_time": 18.294145345687866, "average_score": 58.6, "top_score": 58.6}, "results": [{"rank": 1, "candidate_name": "AKURATHI SASIDHAR", "file_name": "sasidhar.pdf", "overall_score": 58.6, "skills_match": 23.33333333333333, "experience_match": 56.666666666666664, "education_match": 90, "keywords_match": 75.0, "overall_relevance": 82, "matching_skills": "Python, MySQL", "missing_skills": "Django, PostgreSQL, REST API, Git, AWS, Azure, Docker, Machine Learning", "experience_relevance": "Estimated experience level matches 57% of requirements", "recommendations": "Consider developing skills in: Django, PostgreSQL, REST API, Git, AWS; Gain more experience to meet the 3+ years requirement"}]}