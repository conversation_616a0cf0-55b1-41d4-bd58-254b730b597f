#!/usr/bin/env python3
"""
Debug Skills Matching
Check why matching_skills is empty when there should be matches
"""

def debug_skills_matching():
    """Debug the skills matching process"""
    print("🔍 DEBUGGING SKILLS MATCHING")
    print("=" * 50)
    
    # Actual data from the recent run
    resume_skills = ['Python', 'MySQL', 'JSON conversion', 'Data Structures', 'Object-oriented programming']
    job_required_skills = ['Python', 'Django', 'Flask', 'PostgreSQL', 'REST API', 'Git']
    job_preferred_skills = ['AWS', 'Docker', 'Machine Learning', 'Agile development']
    
    print(f"📄 Resume Skills: {resume_skills}")
    print(f"📋 Job Required Skills: {job_required_skills}")
    print(f"📋 Job Preferred Skills: {job_preferred_skills}")
    
    # Check for exact matches
    all_job_skills = job_required_skills + job_preferred_skills
    exact_matches = []
    
    for resume_skill in resume_skills:
        for job_skill in all_job_skills:
            if resume_skill.lower().strip() == job_skill.lower().strip():
                exact_matches.append(resume_skill)
                print(f"✅ EXACT MATCH: '{resume_skill}' matches '{job_skill}'")
    
    print(f"\n🎯 Exact Matches Found: {exact_matches}")
    
    # Check why MySQL isn't matching
    print(f"\n🔍 MySQL Analysis:")
    print(f"   Resume has 'MySQL': {'MySQL' in resume_skills}")
    print(f"   Job description analysis extracted: {job_required_skills}")
    
    # Let's check the actual job description
    from config import DEFAULT_JOB_DESCRIPTION
    print(f"\n📋 Checking actual job description:")
    job_text = DEFAULT_JOB_DESCRIPTION.lower()
    print(f"   Contains 'mysql': {'mysql' in job_text}")
    print(f"   Contains 'postgresql': {'postgresql' in job_text}")
    
    # The issue might be that Ollama extracted PostgreSQL instead of MySQL
    print(f"\n🤔 Analysis:")
    print(f"   Job description mentions: PostgreSQL, MySQL")
    print(f"   Ollama extracted: PostgreSQL (but not MySQL)")
    print(f"   Resume has: MySQL")
    print(f"   Result: No match because Ollama didn't extract MySQL from job description")
    
    # Test the job matcher's analysis
    print(f"\n🧪 Testing Job Description Analysis:")
    from job_matcher import JobMatcher
    matcher = JobMatcher()
    
    try:
        job_requirements = matcher.analyze_job_description(DEFAULT_JOB_DESCRIPTION)
        if job_requirements:
            print(f"   Ollama extracted required skills: {job_requirements.required_skills}")
            print(f"   Ollama extracted preferred skills: {job_requirements.preferred_skills}")
            
            # Check if MySQL is in the extracted skills
            all_extracted = job_requirements.required_skills + job_requirements.preferred_skills
            mysql_extracted = any('mysql' in skill.lower() for skill in all_extracted)
            print(f"   MySQL extracted by Ollama: {mysql_extracted}")
            
            if not mysql_extracted:
                print(f"   🚨 PROBLEM: Ollama didn't extract MySQL from job description!")
                print(f"   💡 SOLUTION: Need to improve job description analysis")
        else:
            print(f"   ❌ Failed to analyze job description")
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    debug_skills_matching()
